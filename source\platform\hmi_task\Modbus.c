/****************************************Copyright (c)**************************************************
**--------------文件信息--------------------------------------------------------------------------------
**文   件   名: Modbus.c
**创   建   人:	ZhangDi
**最后修改日期: 2016年12月27日
**描        述: 
**
********************************************************************************************************/
#include    "Modbus.h"
#include    "uart.h"
#include    "bi.h"
#include    "ana.h"
#include    "setting.h"
#include	"excitation_def.h"
#include	"control.h"
#define        STANDARD_MODBUS        1

typedef struct _ModbusPro_T
{
    uint8 MdProFlag;
    uint16 MdProStartAddr;
    uint16 MdProNeedNum;
    uint16 MdProFunNode;
    uint8 MdLCDataProOkFlag;//励磁数据是否处理完成标志
    uint8 Buff[MODBUSBUFFLEN];//接收的数据缓存
} ModbusPro_T;

uint32 g_ModbusBuffRcvWr = 0;
uint8 g_ModbusRcvBuff[MODBUSBUFFLEN] = {0};
uint32 g_ModbusRunTimer = 0;
uint8 g_ModbusDzData[100] = {0};
uint8 g_ModbusAddr = 1;//NOTEL Modbus地址固定为1，只给屏幕使用
ModbusPro_T ModbusPro = {0};

uint8 g_channalN;
uint8 g_Crt_Com;

// 出口传动测试状态变量
uint64_t g_do_state_test = 0;

/***Modbus开关量表**/
s_ModbusRefTab g_BIModbusRefTab[]=
{
    //开关量状态字
    //开关量状态字1(保护CPU)
	{ .desc = "modBi.bi_in01" ,.addr = 0x0000},
	{ .desc = "modBi.bi_in02" ,.addr = 0x0001},
	{ .desc = "modBi.bi_in03" ,.addr = 0x0002},
	{ .desc = "modBi.bi_in04" ,.addr = 0x0003},
	{ .desc = "modBi.bi_in05" ,.addr = 0x0004},
	{ .desc = "modBi.bi_in06" ,.addr = 0x0005},
	{ .desc = "modBi.bi_in07" ,.addr = 0x0006},
	{ .desc = "modBi.bi_in08" ,.addr = 0x0007},
	{ .desc = "modBi.bi_in09" ,.addr = 0x0008},
	{ .desc = "modBi.bi_in10" ,.addr = 0x0009},
	{ .desc = "modBi.bi_in11",.addr = 0x000A},
	{ .desc = "modBi.bi_in12",.addr = 0x000B},
	{ .desc = "modBi.bi_in13",.addr = 0x000C},
	{ .desc = "modBi.bi_in14",.addr = 0x000D},
	{ .desc = "modBi.bi_in15",.addr = 0x000E},
	{ .desc = "modBi.bi_in16",.addr = 0x000F},
    //开关量状态字2(保护CPU)
	{ .desc = "modBi.bi_in17",.addr = 0x0010},
	{ .desc = "modBi.bi_in18",.addr = 0x0011},
	{ .desc = "modBi.bi_in19",.addr = 0x0012},
	{ .desc = "modBi.bi_in20",.addr = 0x0013},
	{ .desc = "modBi.bi_in21",.addr = 0x0014},
	{ .desc = "modBi.bi_in22",.addr = 0x0015},
	{ .desc = "modBi.bi_in23",.addr = 0x0016},
	{ .desc = "modBi.bi_in24",.addr = 0x0017},
	{ .desc = "modBi.bi_in25",.addr = 0x0018},
	{ .desc = "modBi.bi_in26",.addr = 0x0019},
	{ .desc = "modBi.bi_in27",.addr = 0x001A},
	{ .desc = "modBi.bi_in28",.addr = 0x001B},
	{ .desc = "modBi.bi_in29",.addr = 0x001C},
	{ .desc = "modBi.bi_in30",.addr = 0x001D},
	{ .desc = "modGeneral.bi_const0",.addr = 0x001E}, //备用
	{ .desc = "modGeneral.bi_const0",.addr = 0x001F}, //备用

    //开关量状态字3(励磁CPU)
    { .desc = "excitation_bi_in01",.addr = 0x0020},
    { .desc = "excitation_bi_in02",.addr = 0x0021},
    { .desc = "excitation_bi_in03",.addr = 0x0022},
    { .desc = "excitation_bi_in04",.addr = 0x0023},
    { .desc = "excitation_bi_in05",.addr = 0x0024},
    { .desc = "excitation_bi_in06",.addr = 0x0025},
    { .desc = "excitation_bi_in07",.addr = 0x0026},
    { .desc = "excitation_bi_in08",.addr = 0x0027},
    { .desc = "excitation_state01",.addr = 0x0028},
    { .desc = "excitation_state02",.addr = 0x0029},
    { .desc = "excitation_state03",.addr = 0x002A},
    { .desc = "excitation_state04",.addr = 0x002B},
    { .desc = "excitation_state05",.addr = 0x002C},
    { .desc = "excitation_state06",.addr = 0x002D},
    { .desc = "excitation_state07",.addr = 0x002E},
    { .desc = "excitation_state08",.addr = 0x002F},
    //开关量状态字4(励磁CPU)
    { .desc = "excitation_state09",.addr = 0x0030},
    { .desc = "excitation_state10",.addr = 0x0031},
    { .desc = "excitation_state11",.addr = 0x0032},
    { .desc = "excitation_state12",.addr = 0x0033},
    { .desc = "excitation_state13",.addr = 0x0034},
    { .desc = "excitation_state14",.addr = 0x0035},
    { .desc = "excitation_state15",.addr = 0x0036},
    { .desc = "excitation_state16",.addr = 0x0037},
    { .desc = "excitation_state17",.addr = 0x0038},
    { .desc = "excitation_state18",.addr = 0x0039},
    { .desc = "excitation_state19",.addr = 0x003A},
    { .desc = "excitation_state20",.addr = 0x003B},
    { .desc = "excitation_state21",.addr = 0x003C},
    { .desc = "excitation_state22",.addr = 0x003D},
    { .desc = "excitation_state23",.addr = 0x003E},
    { .desc = "excitation_state24",.addr = 0x003F},

    // 保护CPU状态数据字
    //状态数据字1
    { .desc = "device_Bsj"          ,.addr = 0x0040},
    { .desc = "device_prot_err"          ,.addr = 0x0041},//TODO 保护程序异常
    { .desc = "device_excitation_err"          ,.addr = 0x0042},
    { .desc = "device_plc_err"          ,.addr = 0x0043},
    { .desc = "device_cpu_err"          ,.addr = 0x0044},
    { .desc = "device_ad_err"          ,.addr = 0x0045},
    { .desc = "device_do_err"          ,.addr = 0x0046},
    { .desc = "modSelfChk.bj_all"          ,.addr = 0x0047},
    { .desc = "modPTFail.pt_dx"               ,.addr = 0x0048}, //PT断线
    { .desc = "modCTFail.ct_dx"               ,.addr = 0x0049}, //CT断线
    { .desc = "modSelfChk.bj_phaseq"          ,.addr = 0x004A}, //相序异常
    { .desc = "modOverLoad.op_overload_alarm" ,.addr = 0x004B}, //过负荷告警
    { .desc = "modOverVol.op_ov_alarm"          ,.addr = 0x004C},
    { .desc = "modOverSpeed.op_overspeed_alarm" ,.addr = 0x004D},
    { .desc = "modGeneral.bi_const0"          ,.addr = 0x004E},//TODO 温度
    { .desc = "modSelfChk.bj_thwj"            ,.addr = 0x004F}, //控制回路异常

    //状态数据字2
    { .desc = "modSelfChk.bj_qd24v"           ,.addr = 0x0050}, //长期启动告警
	{ .desc = "modSelfChk.bj_outTest"         ,.addr = 0x0051}, //出口传动使能
    { .desc = "modGeneral.bi_const0"          ,.addr = 0x0052}, //备用

    { .desc = "modSynCtr.syn_start"         ,.addr =      0x0053}, // 同期
    { .desc = "modSynCtr.local_success"     ,.addr =      0x0054},
    { .desc = "modSynCtr.local_uhig"        ,.addr =      0x0055},
    { .desc = "modSynCtr.local_u_low"       ,.addr =      0x0056},
    { .desc = "modSynCtr.local_vdif"        ,.addr =      0x0057},
    { .desc = "modSynCtr.local_fdif"        ,.addr =      0x0058},
    { .desc = "modSynCtr.local_adif"        ,.addr =      0x0059},
    { .desc = "modSynCtr.local_dfdif"       ,.addr =      0x005A},
    { .desc = "modSynCtr.local_ptdx"        ,.addr =      0x005B},
    { .desc = "modSynCtr.local_U_OL"        ,.addr =      0x005C},
    { .desc = "modSynCtr.local_F_OL"        ,.addr =      0x005D},
    { .desc = "modSynCtr.flg_pt_Dscn"       ,.addr =      0x005E},
    { .desc = "modSynCtr.flg_ASV"           ,.addr =      0x005F},


    //状态数据字3
    { .desc = "modBo.flg_qd"          ,.addr = 0x0060}, // 保护类
    { .desc = "modOverCur01.op_oc"          ,.addr = 0x0061},
    { .desc = "modOverCur02.op_oc"          ,.addr = 0x0062},
    { .desc = "modSglOverCur01.op_sgl_oc"          ,.addr = 0x0063},
    { .desc = "modNegOverCur.op_neg_oc"          ,.addr = 0x0064},
    { .desc = "modOverVol.op_ov"          ,.addr = 0x0065},
    { .desc = "modNoVol.op_nv"          ,.addr = 0x0066},
    { .desc = "modUnderExc.op_exc"          ,.addr = 0x0067},
    { .desc = "modUnderFreq.op_uf"          ,.addr = 0x0068},
    { .desc = "modOverFreq.op_of"          ,.addr = 0x0069},
    { .desc = "modRvsPwr.op_rvspwr"          ,.addr = 0x006A},
    { .desc = "modtemp.op_temp1"          ,.addr = 0x006B},//TODO 温度
    { .desc = "modtemp.op_temp2"          ,.addr = 0x006C},
    { .desc = "modtemp.op_temp3"          ,.addr = 0x006D},
    { .desc = "modtemp.op_temp4"          ,.addr = 0x006E},
    { .desc = "modtemp.op_temp5"          ,.addr = 0x006F},

    //状态数据字4
    { .desc = "modtemp.op_temp6"          ,.addr = 0x0070},
    { .desc = "modtemp.op_temp7"          ,.addr = 0x0071},
    { .desc = "modtemp.op_temp8"          ,.addr = 0x0072},
    { .desc = "modNonElec.trip_nonElec_1"          ,.addr = 0x0073},
    { .desc = "modNonElec.trip_nonElec_2"          ,.addr = 0x0074},
    { .desc = "modOverSpeed.op_overspeed"          ,.addr = 0x0075},
    { .desc = "modUnderExc.ovbi_exc"          ,.addr = 0x0076},
    { .desc = "modNoVol.ovbi_nv"              ,.addr = 0x0077},
    { .desc = "modUnderFreq.ovbi_uf"          ,.addr = 0x0078},
    { .desc = "modGeneral.ovbi_rmt_strap"          ,.addr = 0x0079},
    { .desc = "modGeneral.ovbi_rmt_grp"          ,.addr = 0x007A},
    { .desc = "modGeneral.ovbi_rmt_set"          ,.addr = 0x007B},

    { .desc = "modBo.bi_out01"          ,.addr = 0x007C},
    { .desc = "modBo.bi_out02"          ,.addr = 0x007D},
    { .desc = "modBo.bi_out03"          ,.addr = 0x007E},
    { .desc = "modBo.bi_out04"          ,.addr = 0x007F},
    { .desc = "modBo.bi_out05"          ,.addr = 0x0080},
    { .desc = "modBo.bi_out06"          ,.addr = 0x0081},
    { .desc = "modBo.bi_out07"          ,.addr = 0x0082},
    { .desc = "modBo.bi_out08"          ,.addr = 0x0083},
    { .desc = "modBo.bi_out09"          ,.addr = 0x0084},
    { .desc = "modBo.bi_out10"          ,.addr = 0x0085},
    { .desc = "modBo.bi_out11"          ,.addr = 0x0086},
    { .desc = "modBo.bi_out12"          ,.addr = 0x0087},
    { .desc = "modBo.bi_out13"          ,.addr = 0x0088},
    { .desc = "modBo.bi_out14"          ,.addr = 0x0089},
    { .desc = "modBo.bi_out15"          ,.addr = 0x008A},
    { .desc = "modBo.bi_out16"          ,.addr = 0x008B},
    { .desc = "modBo.bi_out17"          ,.addr = 0x008C},
    { .desc = "modBo.bi_out18"          ,.addr = 0x008D},
    { .desc = "modBo.bi_out19"          ,.addr = 0x008E},
    { .desc = "modBo.bi_out20"          ,.addr = 0x008F},
    { .desc = "modBo.bi_out21"          ,.addr = 0x0090},
    { .desc = "modBo.bi_out22"          ,.addr = 0x0091},
    { .desc = "modBo.bi_out23"          ,.addr = 0x0092},
    { .desc = "modBo.bi_out24"          ,.addr = 0x0093},
    { .desc = "modBo.bi_out25"          ,.addr = 0x0094},
    { .desc = "modBo.bi_out26"          ,.addr = 0x0095},
    { .desc = "modBo.bi_out27"          ,.addr = 0x0096},
    { .desc = "modBo.bi_out28"          ,.addr = 0x0097},
    { .desc = "modBo.bi_out29"          ,.addr = 0x0098},
    { .desc = "modBo.bi_out30"          ,.addr = 0x0099},
    { .desc = "modBo.bi_out31"          ,.addr = 0x009A},
    { .desc = "modBo.bi_out32"          ,.addr = 0x009B},

    { .desc = "excitation_bi_out01"    ,.addr = 0x009C}, //TODO 励磁开出
    { .desc = "excitation_bi_out02"    ,.addr = 0x009D},
    { .desc = "excitation_bi_out03"    ,.addr = 0x009E},
    { .desc = "excitation_bi_out04"    ,.addr = 0x009F},
    { .desc = "excitation_bi_out05"    ,.addr = 0x00A0},

    //自定义状态数据（PLC）
   { .desc = "plc_var2_001"          ,.addr = 0x00A1},
   { .desc = "plc_var2_002"          ,.addr = 0x00A2},
   { .desc = "plc_var2_003"          ,.addr = 0x00A3},
   { .desc = "plc_var2_004"          ,.addr = 0x00A4},
   { .desc = "plc_var2_005"          ,.addr = 0x00A5},
   { .desc = "plc_var2_006"          ,.addr = 0x00A6},
   { .desc = "plc_var2_007"          ,.addr = 0x00A7},
   { .desc = "plc_var2_008"          ,.addr = 0x00A8},
   { .desc = "plc_var2_009"          ,.addr = 0x00A9},
   { .desc = "plc_var2_010"          ,.addr = 0x00AA},
   { .desc = "plc_var2_011"          ,.addr = 0x00AB},
   { .desc = "plc_var2_012"          ,.addr = 0x00AC},
   { .desc = "plc_var2_013"          ,.addr = 0x00AD},
   { .desc = "plc_var2_014"          ,.addr = 0x00AE},
   { .desc = "plc_var2_015"          ,.addr = 0x00AF},
   { .desc = "plc_var2_016"          ,.addr = 0x00B0},
   { .desc = "plc_var2_017"          ,.addr = 0x00B1},
   { .desc = "plc_var2_018"          ,.addr = 0x00B2},
   { .desc = "plc_var2_019"          ,.addr = 0x00B3},
   { .desc = "plc_var2_020"          ,.addr = 0x00B4},
   { .desc = "plc_var2_021"          ,.addr = 0x00B5},
   { .desc = "plc_var2_022"          ,.addr = 0x00B6},
   { .desc = "plc_var2_023"          ,.addr = 0x00B7},
   { .desc = "plc_var2_024"          ,.addr = 0x00B8},
   { .desc = "plc_var2_025"          ,.addr = 0x00B9},
   { .desc = "plc_var2_026"          ,.addr = 0x00BA},
   { .desc = "plc_var2_027"          ,.addr = 0x00BB},
   { .desc = "plc_var2_028"          ,.addr = 0x00BC},
   { .desc = "plc_var2_029"          ,.addr = 0x00BD},
   { .desc = "plc_var2_030"          ,.addr = 0x00BE},
   { .desc = "plc_var2_031"          ,.addr = 0x00BF},
   { .desc = "plc_var2_032"          ,.addr = 0x00C0},
   { .desc = "plc_var2_033"          ,.addr = 0x00C1},
   { .desc = "plc_var2_034"          ,.addr = 0x00C2},
   { .desc = "plc_var2_035"          ,.addr = 0x00C3},
   { .desc = "plc_var2_036"          ,.addr = 0x00C4},
   { .desc = "plc_var2_037"          ,.addr = 0x00C5},
   { .desc = "plc_var2_038"          ,.addr = 0x00C6},
   { .desc = "plc_var2_039"          ,.addr = 0x00C7},
   { .desc = "plc_var2_040"          ,.addr = 0x00C8},
   { .desc = "plc_var2_041"          ,.addr = 0x00C9},
   { .desc = "plc_var2_042"          ,.addr = 0x00CA},
   { .desc = "plc_var2_043"          ,.addr = 0x00CB},
   { .desc = "plc_var2_044"          ,.addr = 0x00CC},
   { .desc = "plc_var2_045"          ,.addr = 0x00CD},
   { .desc = "plc_var2_046"          ,.addr = 0x00CE},
   { .desc = "plc_var2_047"          ,.addr = 0x00CF},
   { .desc = "plc_var2_048"          ,.addr = 0x00D0},
   { .desc = "plc_var2_049"          ,.addr = 0x00D1},
   { .desc = "plc_var2_050"          ,.addr = 0x00D2},
   { .desc = "plc_var2_051"          ,.addr = 0x00D3},
   { .desc = "plc_var2_052"          ,.addr = 0x00D4},
   { .desc = "plc_var2_053"          ,.addr = 0x00D5},
   { .desc = "plc_var2_054"          ,.addr = 0x00D6},
   { .desc = "plc_var2_055"          ,.addr = 0x00D7},
   { .desc = "plc_var2_056"          ,.addr = 0x00D8},
   { .desc = "plc_var2_057"          ,.addr = 0x00D9},
   { .desc = "plc_var2_058"          ,.addr = 0x00DA},
   { .desc = "plc_var2_059"          ,.addr = 0x00DB},
   { .desc = "plc_var2_060"          ,.addr = 0x00DC},
   { .desc = "plc_var2_061"          ,.addr = 0x00DD},
   { .desc = "plc_var2_062"          ,.addr = 0x00DE},
   { .desc = "plc_var2_063"          ,.addr = 0x00DF},
   { .desc = "plc_var2_064"          ,.addr = 0x00E0},
   { .desc = "plc_var2_065"          ,.addr = 0x00E1},
   { .desc = "plc_var2_066"          ,.addr = 0x00E2},
   { .desc = "plc_var2_067"          ,.addr = 0x00E3},
   { .desc = "plc_var2_068"          ,.addr = 0x00E4},
   { .desc = "plc_var2_069"          ,.addr = 0x00E5},
   { .desc = "plc_var2_070"          ,.addr = 0x00E6},
   { .desc = "plc_var2_071"          ,.addr = 0x00E7},
   { .desc = "plc_var2_072"          ,.addr = 0x00E8},
   { .desc = "plc_var2_073"          ,.addr = 0x00E9},
   { .desc = "plc_var2_074"          ,.addr = 0x00EA},
   { .desc = "plc_var2_075"          ,.addr = 0x00EB},
   { .desc = "plc_var2_076"          ,.addr = 0x00EC},
   { .desc = "plc_var2_077"          ,.addr = 0x00ED},
   { .desc = "plc_var2_078"          ,.addr = 0x00EE},
   { .desc = "plc_var2_079"          ,.addr = 0x00EF},
   { .desc = "plc_var2_080"          ,.addr = 0x00F0},
   { .desc = "plc_var2_081"          ,.addr = 0x00F1},
   { .desc = "plc_var2_082"          ,.addr = 0x00F2},
   { .desc = "plc_var2_083"          ,.addr = 0x00F3},  
   { .desc = "plc_var2_084"          ,.addr = 0x00F4},
   { .desc = "plc_var2_085"          ,.addr = 0x00F5},
   { .desc = "plc_var2_086"          ,.addr = 0x00F6},
   { .desc = "plc_var2_087"          ,.addr = 0x00F7},
   { .desc = "plc_var2_088"          ,.addr = 0x00F8},
   { .desc = "plc_var2_089"          ,.addr = 0x00F9},
   { .desc = "plc_var2_090"          ,.addr = 0x00FA},
   { .desc = "plc_var2_091"          ,.addr = 0x00FB},
   { .desc = "plc_var2_092"          ,.addr = 0x00FC},
   { .desc = "plc_var2_093"          ,.addr = 0x00FD},
   { .desc = "plc_var2_094"          ,.addr = 0x00FE},
   { .desc = "plc_var2_095"          ,.addr = 0x00FF},
   { .desc = "plc_var2_096"          ,.addr = 0x0100},
   { .desc = "plc_var2_097"          ,.addr = 0x0101},
   { .desc = "plc_var2_098"          ,.addr = 0x0102},
   { .desc = "plc_var2_099"          ,.addr = 0x0103},
   { .desc = "plc_var2_100"          ,.addr = 0x0104},
   { .desc = "plc_var2_101"          ,.addr = 0x0105},
   { .desc = "plc_var2_102"          ,.addr = 0x0106},
   { .desc = "plc_var2_103"          ,.addr = 0x0107},
   { .desc = "plc_var2_104"          ,.addr = 0x0108},
   { .desc = "plc_var2_105"          ,.addr = 0x0109},
   { .desc = "plc_var2_106"          ,.addr = 0x010A},
   { .desc = "plc_var2_107"          ,.addr = 0x010B},
   { .desc = "plc_var2_108"          ,.addr = 0x010C},
   { .desc = "plc_var2_109"          ,.addr = 0x010D},
   { .desc = "plc_var2_110"          ,.addr = 0x010E},
   { .desc = "plc_var2_111"          ,.addr = 0x010F},
   { .desc = "plc_var2_112"          ,.addr = 0x0110},
   { .desc = "plc_var2_113"          ,.addr = 0x0111},
   { .desc = "plc_var2_114"          ,.addr = 0x0112},
   { .desc = "plc_var2_115"          ,.addr = 0x0113},
   { .desc = "plc_var2_116"          ,.addr = 0x0114},
   { .desc = "plc_var2_117"          ,.addr = 0x0115},
   { .desc = "plc_var2_118"          ,.addr = 0x0116},
   { .desc = "plc_var2_119"          ,.addr = 0x0117},
   { .desc = "plc_var2_120"          ,.addr = 0x0118},
   { .desc = "plc_var2_121"          ,.addr = 0x0119},
   { .desc = "plc_var2_122"          ,.addr = 0x011A},
   { .desc = "plc_var2_123"          ,.addr = 0x011B},
   { .desc = "plc_var2_124"          ,.addr = 0x011C},
   { .desc = "plc_var2_125"          ,.addr = 0x011D},
   { .desc = "plc_var2_126"          ,.addr = 0x011E},
   { .desc = "plc_var2_127"          ,.addr = 0x011F},
   { .desc = "plc_var2_128"          ,.addr = 0x0120}
};
#define MODBUS_SWITCH_NUMS (sizeof(g_BIModbusRefTab) / sizeof(s_ModbusRefTab))

/*Modbus模拟量表*/
s_ModbusRefTab g_ANAModbusRefTab[]=
{
    //保护CPU模拟量
	{ .desc = "modGeneral.curSetNo" , .type = e_uint16, .point = 0, .addr = 0x1000},
    { .desc = "modGeneral.fm", .type = e_uint16, .point = 2, .addr = 0x1001},
    { .desc = "modGeneral.fx", .type = e_uint16, .point = 2, .addr = 0x1002},
    { .desc = "modGeneral.rate_percent", .type = e_uint16, .point = 1, .addr = 0x1003},// 转速百分比
    { .desc = "modGeneral.rate", .type = e_uint16, .point = 0, .addr = 0x1004},// 发电机转速
	{ .desc = "modVol.ua" ,.type = e_uint16, .point = 2, .addr = 0x1005},//一次值???
	{ .desc = "modVol.ub" ,.type = e_uint16, .point = 2, .addr = 0x1006},
	{ .desc = "modVol.uc" ,.type = e_uint16, .point = 2, .addr = 0x1007},
	{ .desc = "modVol.u0" ,.type = e_int16, .point = 2, .addr = 0x1008},//TODO 励磁电压uf
	{ .desc = "modVol.ux" ,.type = e_uint16, .point = 2, .addr = 0x1009},
	{ .desc = "modCur.ia" ,.type = e_uint16, .point = 2, .addr = 0x100A},
	{ .desc = "modCur.ib" ,.type = e_uint16, .point = 2, .addr = 0x100B},
	{ .desc = "modCur.ic" ,.type = e_uint16, .point = 2, .addr = 0x100C},

    { .desc = "modMeasData.ua",	.type = e_uint16, .point = 2, .addr = 0x100D},
	{ .desc = "modMeasData.ub",	.type = e_uint16, .point = 2, .addr = 0x100E},
	{ .desc = "modMeasData.uc",	.type = e_uint16, .point = 2, .addr = 0x100F},
    { .desc = "modMeasData.ia",	.type = e_uint16, .point = 2, .addr = 0x1010},
	{ .desc = "modMeasData.ib",	.type = e_uint16, .point = 2, .addr = 0x1011},
	{ .desc = "modMeasData.ic",	.type = e_uint16, .point = 2, .addr = 0x1012},

	{ .desc = "modVol.uab" ,.type = e_uint16, .point = 2, .addr = 0x1013},
	{ .desc = "modVol.ubc" ,.type = e_uint16, .point = 2, .addr = 0x1014},
	{ .desc = "modVol.uca" ,.type = e_uint16, .point = 2, .addr = 0x1015},

	{ .desc = "modMeasData.P" ,.type = e_int32, .point = 3, .addr = 0x1016}, //低字节 //TODO
	//{ .desc = "modMeasData.P" ,.type = e_int32, .point = 3, .addr = 0x1017}, //高字节
	{ .desc = "modMeasData.Q" ,.type = e_int32, .point = 3, .addr = 0x1018}, //低字节 //TODO
	//{ .desc = "modMeasData.Q" ,.type = e_int32, .point = 3, .addr = 0x1019}, //高字节

	{ .desc = "modMeasData.cos" ,.type = e_int16, .point = 3, .addr = 0x101A},
	{ .desc = "modCur.i1" ,.type = e_uint16, .point = 2, .addr = 0x101B},
	{ .desc = "modCur.i2" ,.type = e_uint16, .point = 3, .addr = 0x101C},
	{ .desc = "modVol.u1" ,.type = e_uint16, .point = 2, .addr = 0x101D},
	{ .desc = "modVol.u2" ,.type = e_uint16, .point = 2, .addr = 0x101E},
	{ .desc = "modGetAngle.ang07" ,.type = e_int16, .point = 1, .addr = 0x101F},//Ang(Ua-Ub) //电流电压不为0时才正常
	{ .desc = "modGetAngle.ang08" ,.type = e_int16, .point = 1, .addr = 0x1020},//Ang(Ub-Uc)
	{ .desc = "modGetAngle.ang09" ,.type = e_int16, .point = 1, .addr = 0x1021},//Ang(Uc-Ua)
	{ .desc = "modGetAngle.ang06" ,.type = e_int16, .point = 1, .addr = 0x1022},//Ang(Ux-Ua)
	{ .desc = "modGetAngle.ang01" ,.type = e_int16, .point = 1, .addr = 0x1023},//Ang(Ia-Ib)
	{ .desc = "modGetAngle.ang02" ,.type = e_int16, .point = 1, .addr = 0x1024},//Ang(Ib-Ic)
	{ .desc = "modGetAngle.ang03" ,.type = e_int16, .point = 1, .addr = 0x1025},//Ang(Ic-Ia)

	{ .desc = "modVol.ua_prim" ,.type = e_int16, .point = 3, .addr = 0x1026},
	{ .desc = "modVol.ub_prim" ,.type = e_int16, .point = 3, .addr = 0x1027},
	{ .desc = "modVol.uc_prim" ,.type = e_int16, .point = 3, .addr = 0x1028},
	{ .desc = "modVol.uab_prim" ,.type = e_int16, .point = 3, .addr = 0x1029},
	{ .desc = "modVol.ubc_prim" ,.type = e_int16, .point = 3, .addr = 0x102A},
	{ .desc = "modVol.uca_prim" ,.type = e_int16, .point = 3, .addr = 0x102B},

	{ .desc = "modCur.ia_prim" ,.type = e_int32, .point = 2, .addr = 0x102C}, //低字节 //!!! 没错
	//{ .desc = "modCur.ia_prim" ,.type = e_int32, .point = 2, .addr = 0x102D}, //高字节
	{ .desc = "modCur.ib_prim" ,.type = e_int32, .point = 2, .addr = 0x102E}, //低字节
	//{ .desc = "modCur.ib_prim" ,.type = e_int32, .point = 2, .addr = 0x102F}, //高字节
	{ .desc = "modCur.ic_prim" ,.type = e_int32, .point = 2, .addr = 0x1030}, //低字节
	//{ .desc = "modCur.ic_prim" ,.type = e_int32, .point = 2, .addr = 0x1031}, //高字节
	{ .desc = "modMeasData.P_prim" ,.type = e_int32, .point = 3, .addr = 0x1032}, //低字节
	//{ .desc = "modMeasData.P_prim" ,.type = e_int32, .point = 3, .addr = 0x1033}, //高字节
	{ .desc = "modMeasData.Q_prim" ,.type = e_int32, .point = 3, .addr = 0x1034}, //低字节
	//{ .desc = "modMeasData.Q_prim" ,.type = e_int32, .point = 3, .addr = 0x1035}, //高字节

    {.desc = "modGeneral.bi_const0", .type = e_int16, .point = 2, .addr = 0x1036},//TODO 直流模拟量1
    {.desc = "modGeneral.bi_const0", .type = e_int16, .point = 2, .addr = 0x1037},
    {.desc = "modGeneral.bi_const0", .type = e_int16, .point = 2, .addr = 0x1038},
    {.desc = "modGeneral.bi_const0", .type = e_int16, .point = 2, .addr = 0x1039},

    {.desc = "modMeasData.tempvalue1", .type = e_int16, .point = 1, .addr = 0x103A},//TODO 温度1
    {.desc = "modMeasData.tempvalue2", .type = e_int16, .point = 1, .addr = 0x103B},
    {.desc = "modMeasData.tempvalue3", .type = e_int16, .point = 1, .addr = 0x103C},
    {.desc = "modMeasData.tempvalue4", .type = e_int16, .point = 1, .addr = 0x103D},
    {.desc = "modMeasData.tempvalue5", .type = e_int16, .point = 1, .addr = 0x103E},
    {.desc = "modMeasData.tempvalue6", .type = e_int16, .point = 1, .addr = 0x103F},
    {.desc = "modMeasData.tempvalue7", .type = e_int16, .point = 1, .addr = 0x1040},
    {.desc = "modMeasData.tempvalue8", .type = e_int16, .point = 1, .addr = 0x1041},

    {.desc = "modSynCtr.v_dif_show", .type = e_uint16, .point = 0, .addr = 0x1042},//!!! 同期
    {.desc = "modSynCtr.f_dif_show", .type = e_uint16, .point = 2, .addr = 0x1043},
    {.desc = "modGetAngle.angle_dif_show", .type = e_uint16, .point = 2, .addr = 0x1044},

    {.desc = "modVol.ua_h3", .type = e_uint16, .point = 1, .addr = 0x1045},
    {.desc = "modVol.ub_h3", .type = e_uint16, .point = 2, .addr = 0x1046},
    {.desc = "modVol.uc_h3", .type = e_uint16, .point = 2, .addr = 0x1047},

    { .desc = "modCur.3i0" ,.type = e_uint16, .point = 2, .addr = 0x1048},
	{ .desc = "modVol.3u0" ,.type = e_uint16, .point = 2, .addr = 0x1049},

    //励磁CPU模拟量
	{ .desc = "excitation.lc_ana01" ,.type = e_uint16, .point = 2, .addr = 0x104A},
	{ .desc = "excitation.lc_ana02" ,.type = e_uint16, .point = 2, .addr = 0x104B},
	{ .desc = "excitation.lc_ana03" ,.type = e_uint16, .point = 2, .addr = 0x104C},
	{ .desc = "excitation.lc_ana04" ,.type = e_uint16, .point = 2, .addr = 0x104D},
	{ .desc = "excitation.lc_ana05" ,.type = e_uint16, .point = 2, .addr = 0x104E},
	{ .desc = "excitation.lc_ana06" ,.type = e_uint16, .point = 2, .addr = 0x104F},
	{ .desc = "excitation.lc_ana07" ,.type = e_uint16, .point = 2, .addr = 0x1050},

	{ .desc = "excitation.lc_ana08" ,.type = e_int16, .point = 2, .addr = 0x1051},
	{ .desc = "excitation.lc_ana09" ,.type = e_int16, .point = 2, .addr = 0x1052},
	{ .desc = "excitation.lc_ana10" ,.type = e_int16, .point = 3, .addr = 0x1053},
	{ .desc = "excitation.lc_ana11" ,.type = e_int16, .point = 2, .addr = 0x1054},

	{ .desc = "excitation.lc_ana12" ,.type = e_uint16, .point = 2, .addr = 0x1055},
	{ .desc = "excitation.lc_ana13" ,.type = e_uint16, .point = 2, .addr = 0x1056},
	{ .desc = "excitation.lc_ana14" ,.type = e_uint16, .point = 2, .addr = 0x1057},
	{ .desc = "excitation.lc_ana15" ,.type = e_uint16, .point = 3, .addr = 0x1058},
	{ .desc = "excitation.lc_ana16" ,.type = e_uint16, .point = 3, .addr = 0x1059},
	{ .desc = "excitation.lc_ana17" ,.type = e_uint16, .point = 0, .addr = 0x105A},

	{ .desc = "excitation.lc_ana18" ,.type = e_int16, .point = 0, .addr = 0x105B},
	{ .desc = "excitation.lc_ana19" ,.type = e_int16, .point = 0, .addr = 0x105C},
	{ .desc = "excitation.lc_ana20" ,.type = e_uint16, .point = 2, .addr = 0x105D},
	{ .desc = "excitation.lc_ana21" ,.type = e_uint16, .point = 0, .addr = 0x105E},

    //版本信息模拟量
    { .desc = "version.year" ,.type = e_uint16, .point = 0, .addr = 0x105F},
    { .desc = "version.month" ,.type = e_uint16, .point = 0, .addr = 0x1060},
    { .desc = "version.day" ,.type = e_uint16, .point = 0, .addr = 0x1061},
    { .desc = "version.app_ver1" ,.type = e_uint16, .point = 0, .addr = 0x1062},
    { .desc = "version.app_ver2" ,.type = e_uint16, .point = 0, .addr = 0x1063},
    { .desc = "version.plc_ver1" ,.type = e_uint16, .point = 0, .addr = 0x1064},
    { .desc = "version.plc_ver2" ,.type = e_uint16, .point = 0, .addr = 0x1065},
    { .desc = "version.app_crc" ,.type = e_uint32, .point = 0, .addr = 0x1066},

    //TODO PLC输出模拟量

};
#define MODBUS_ANALOG_NUMS (sizeof(g_ANAModbusRefTab) / sizeof(s_ModbusRefTab))

/*Modbus控制表*/
s_ModbusRefTab g_modbusCtlRefTab[] =
{
    { .desc = "dev_rst",      .addr = 241},   // 设备复位
    { .desc = "start_wave",   .addr = 245},   // 启动录波
    { .desc = "change_setNo", .addr = 244}   // 切换定值组
};
#define MODBUS_CTL_NUMS (sizeof(g_modbusCtlRefTab) / sizeof(s_ModbusRefTab))

/*Modbus软压板表*/
s_ModbusRefTab g_modbusCtlRefTab2[] =
{
    { .desc = "vbi_exc",      .addr = 1},
    { .desc = "vbi_nv",   .addr = 2},
    { .desc = "vbi_uf", .addr = 3},
    { .desc = "vbi_phaseq", .addr = 4}
};
#define MODBUS_CTL2_NUMS (sizeof(g_modbusCtlRefTab2) / sizeof(s_ModbusRefTab))

/*Modbus控制表3*/
s_ModbusRefTab g_modbusCtlRefTab3[] =
{
    { .desc = "plc_var_001", .addr = 51},
    { .desc = "plc_var_002", .addr = 52},
    { .desc = "plc_var_003", .addr = 53},
    { .desc = "plc_var_004", .addr = 54},
    { .desc = "plc_var_005", .addr = 55},
    { .desc = "plc_var_006", .addr = 56},
    { .desc = "plc_var_007", .addr = 57},
    { .desc = "plc_var_008", .addr = 58},
    { .desc = "plc_var_009", .addr = 59},
    { .desc = "plc_var_010", .addr = 60},
    { .desc = "plc_var_011", .addr = 61},
    { .desc = "plc_var_012", .addr = 62},
    { .desc = "plc_var_013", .addr = 63},
    { .desc = "plc_var_014", .addr = 64},
    { .desc = "plc_var_015", .addr = 65},
    { .desc = "plc_var_016", .addr = 66},
    { .desc = "plc_var_017", .addr = 67},
    { .desc = "plc_var_018", .addr = 68},
    { .desc = "plc_var_019", .addr = 69},
    { .desc = "plc_var_020", .addr = 70},
    { .desc = "plc_var_021", .addr = 71},
    { .desc = "plc_var_022", .addr = 72},
    { .desc = "plc_var_023", .addr = 73},
    { .desc = "plc_var_024", .addr = 74},
    { .desc = "plc_var_025", .addr = 75},
    { .desc = "plc_var_026", .addr = 76},
    { .desc = "plc_var_027", .addr = 77},
    { .desc = "plc_var_028", .addr = 78},
    { .desc = "plc_var_029", .addr = 79},
    { .desc = "plc_var_030", .addr = 80},
    { .desc = "plc_var_031", .addr = 81},
    { .desc = "plc_var_032", .addr = 82},
    { .desc = "plc_var_033", .addr = 83},
    { .desc = "plc_var_034", .addr = 84},
    { .desc = "plc_var_035", .addr = 85},
    { .desc = "plc_var_036", .addr = 86},
    { .desc = "plc_var_037", .addr = 87},
    { .desc = "plc_var_038", .addr = 88},
    { .desc = "plc_var_039", .addr = 89},
    { .desc = "plc_var_040", .addr = 90},
    { .desc = "plc_var_041", .addr = 91},
    { .desc = "plc_var_042", .addr = 92},
    { .desc = "plc_var_043", .addr = 93},
    { .desc = "plc_var_044", .addr = 94},
    { .desc = "plc_var_045", .addr = 95},
    { .desc = "plc_var_046", .addr = 96},
    { .desc = "plc_var_047", .addr = 97},
    { .desc = "plc_var_048", .addr = 98},
    { .desc = "plc_var_049", .addr = 99},
    { .desc = "plc_var_050", .addr = 100},
    { .desc = "plc_var_051", .addr = 101},
    { .desc = "plc_var_052", .addr = 102},
    { .desc = "plc_var_053", .addr = 103},
    { .desc = "plc_var_054", .addr = 104},
    { .desc = "plc_var_055", .addr = 105},
    { .desc = "plc_var_056", .addr = 106},
    { .desc = "plc_var_057", .addr = 107},
    { .desc = "plc_var_058", .addr = 108},
    { .desc = "plc_var_059", .addr = 109},
    { .desc = "plc_var_060", .addr = 110},
    { .desc = "plc_var_061", .addr = 111},
    { .desc = "plc_var_062", .addr = 112},
    { .desc = "plc_var_063", .addr = 113},
    { .desc = "plc_var_064", .addr = 114}
};
#define MODBUS_CTL3_NUMS (sizeof(g_modbusCtlRefTab3) / sizeof(s_ModbusRefTab))

/*Modbus定值组表*/
s_ModbusRefTab g_allSetModbusRefTab[] =
{
    //保护+顺控定值
    //测量精校定值
    { .desc = "Ima_coeff",    .type = e_int16,.addr = 0x2000},
    { .desc = "Ima_deadZone", .type = e_int16,.addr = 0x2001},
    { .desc = "Imb_coeff",    .type = e_int16,.addr = 0x2002},
    { .desc = "Imb_deadZone", .type = e_int16,.addr = 0x2003},
    { .desc = "Imc_coeff",    .type = e_int16,.addr = 0x2004},
    { .desc = "Imc_deadZone", .type = e_int16,.addr = 0x2005},

    { .desc = "Umx_coeff",    .type = e_int16,.addr = 0x2006},
    { .desc = "Umx_deadZone", .type = e_int16,.addr = 0x2007},
    { .desc = "Uma_coeff",    .type = e_int16,.addr = 0x2008},
    { .desc = "Uma_deadZone", .type = e_int16,.addr = 0x2009},
    { .desc = "Umb_coeff",    .type = e_int16,.addr = 0x200A},
    { .desc = "Umb_deadZone", .type = e_int16,.addr = 0x200B},
    { .desc = "Umc_coeff",    .type = e_int16,.addr = 0x200C},
    { .desc = "Umc_deadZone", .type = e_int16,.addr = 0x200D},

    { .desc = "angle_a",      .type = e_int16,.addr = 0x200E},
    { .desc = "angle_b",      .type = e_int16,.addr = 0x200F},
    { .desc = "angle_c",      .type = e_int16,.addr = 0x2010},
    { .desc = "beiyong",      .type = e_int16,.addr = 0x2011}, //备用

    //保护精校定值
    { .desc = "Ia_coeff",      .type = e_int16,.addr = 0x2018},
	{ .desc = "Ia_angle",      .type = e_int16,.addr = 0x2019},
	{ .desc = "Ib_coeff",      .type = e_int16,.addr = 0x201A},
	{ .desc = "Ib_angle",      .type = e_int16,.addr = 0x201B},
	{ .desc = "Ic_coeff",      .type = e_int16,.addr = 0x201C},
	{ .desc = "Ic_angle",      .type = e_int16,.addr = 0x201D},

    { .desc = "Ux_coeff",      .type = e_int16,.addr = 0x201E},
    { .desc = "Ux_angle",      .type = e_int16,.addr = 0x201F},
	{ .desc = "Ua_coeff",      .type = e_int16,.addr = 0x2020},
	{ .desc = "Ua_angle",      .type = e_int16,.addr = 0x2021},
	{ .desc = "Ub_coeff",      .type = e_int16,.addr = 0x2022},
	{ .desc = "Ub_angle",      .type = e_int16,.addr = 0x2023},
	{ .desc = "Uc_coeff",      .type = e_int16,.addr = 0x2024},
	{ .desc = "Uc_angle",      .type = e_int16,.addr = 0x2025},

    //系统定值
    {.desc = "Rated_Power",              .type = e_uint16,.addr = 0x2032 },
    {.desc = "Rated_Power_Factor",       .type = e_uint16,.addr = 0x2033 },
    {.desc = "Rated_Excitation_Current", .type = e_uint16,.addr = 0x2034 },
    {.desc = "Rated_Speed",              .type = e_uint16,.addr = 0x2035 },
	{ .desc = "sys_un_prim",             .type = e_uint16,.addr = 0x2036 },
	{ .desc = "sys_un_snd",              .type = e_uint16,.addr = 0x2037 },
	{ .desc = "sys_ux_prim",             .type = e_uint16,.addr = 0x2038 },
	{ .desc = "sys_ux_snd",              .type = e_uint16,.addr = 0x2039 },
	{ .desc = "sys_in_prim",             .type = e_uint16,.addr = 0x203A },
	{ .desc = "sys_in_snd",              .type = e_uint16,.addr = 0x203B },

    //直流定值
    {.desc = "excit_volt_min"	    ,.type = e_int16,.addr = 0x2064  },
    {.desc = "excit_volt_max"	    ,.type = e_int16,.addr = 0x2065  },
    {.desc = "shunt_max_current"	,.type = e_uint16,.addr = 0x2066  },
    {.desc = "dc_transmitter1_min"	,.type = e_int16,.addr = 0x2067  },
    {.desc = "dc_transmitter1_max"	,.type = e_int16,.addr = 0x2068  },
    {.desc = "dc_transmitter2_min"	,.type = e_int16,.addr = 0x2069  },
    {.desc = "dc_transmitter2_max"	,.type = e_int16,.addr = 0x206A  },
    {.desc = "dc_transmitter3_min"	,.type = e_int16,.addr = 0x206B  },
    {.desc = "dc_transmitter3_max"	,.type = e_int16,.addr = 0x206C  },
    {.desc = "dc_transmitter4_min"	,.type = e_int16,.addr = 0x206D  },
    {.desc = "dc_transmitter4_max"	,.type = e_int16,.addr = 0x206E  },
    {.desc = "temp1_type"	        ,.type = e_uint16,.addr = 0x206F  },
    {.desc = "temp2_type"	        ,.type = e_uint16,.addr = 0x2070  },
    {.desc = "temp3_type"	        ,.type = e_uint16,.addr = 0x2071  },
    {.desc = "temp4_type"	        ,.type = e_uint16,.addr = 0x2072  },
    {.desc = "temp5_type"	        ,.type = e_uint16,.addr = 0x2073  },
    {.desc = "temp6_type"	        ,.type = e_uint16,.addr = 0x2074  },
    {.desc = "temp7_type"	        ,.type = e_uint16,.addr = 0x2075  },
    {.desc = "temp8_type"	        ,.type = e_uint16,.addr = 0x2076  },

    //保护定值
	{ .desc = "modOverCur01.set_i_oc",.type = e_uint16,.addr = 0x20C8    },
	{ .desc = "modOverCur01.set_t_oc",.type = e_uint16,.addr = 0x20C9    },
	{ .desc = "modOverCur02.set_i_oc",.type = e_uint16,.addr = 0x20CA    },
	{ .desc = "modOverCur02.set_t_oc",.type = e_uint16,.addr = 0x20CB    },

	{ .desc = "set_i_sgl_oc",.type = e_uint16,.addr = 0x20CC    },
	{ .desc = "set_t_sgl_oc",.type = e_uint16,.addr = 0x20CD    },

   	{ .desc = "set_i_neg_oc",.type = e_uint16,.addr = 0x20CE    },
	{ .desc = "set_t_neg_oc",.type = e_uint16,.addr = 0x20CF    },

	{ .desc = "set_i_overload",.type = e_uint16,.addr = 0x20D0    },
	{ .desc = "set_t_overload",.type = e_uint16,.addr = 0x20D1    },

    { .desc = "set_u_ov_alarm",.type = e_uint16,.addr = 0x20D2    },
    { .desc = "set_t_ov_alarm",.type = e_uint16,.addr = 0x20D3   },
    { .desc = "set_u_ov",.type = e_uint16,.addr = 0x20D4    },
    { .desc = "set_t_ov",.type = e_uint16,.addr = 0x20D5    },

	{ .desc = "set_u_nv",.type = e_uint16,.addr = 0x20D6    },
	{ .desc = "set_t_nv",.type = e_uint16,.addr = 0x20D7    },

	{ .desc = "set_f_exc",.type = e_uint16,.addr = 0x20D8    },
	{ .desc = "set_t_exc",.type = e_uint16,.addr = 0x20D9    },

	{ .desc = "set_f_uf",.type = e_uint16,.addr = 0x20DA    },
	{ .desc = "set_t_uf",.type = e_uint16,.addr = 0x20DB    },

	{ .desc = "set_f_of",.type = e_uint16,.addr = 0x20DC    },
	{ .desc = "set_t_of",.type = e_uint16,.addr = 0x20DD    },

	{ .desc = "set_f_rvspwr",.type = e_uint16,.addr = 0x20DE    },
	{ .desc = "set_t_rvspwr",.type = e_uint16,.addr = 0x20DF    },

	{ .desc = "set_i_overspeed_alarm",.type = e_uint16,.addr = 0x20E0    },
	{ .desc = "set_t_overspeed_alarm",.type = e_uint16,.addr = 0x20E1    },
	{ .desc = "set_i_overspeed",.type = e_uint16,.addr = 0x20E2    },
	{ .desc = "set_t_overspeed",.type = e_uint16,.addr = 0x20E3    },

    //温度模块定值
    { .desc = "alm_temp1_p",.type = e_uint16,.addr = 0x20E4    },
    { .desc = "act_temp1_p",.type = e_uint16,.addr = 0x20E5    },
    { .desc = "alm_temp2_p",.type = e_uint16,.addr = 0x20E6    },
    { .desc = "act_temp2_p",.type = e_uint16,.addr = 0x20E7    },
    { .desc = "alm_temp3_p",.type = e_uint16,.addr = 0x20E8    },
    { .desc = "act_temp3_p",.type = e_uint16,.addr = 0x20E9    },
    { .desc = "alm_temp4_p",.type = e_uint16,.addr = 0x20EA    },
    { .desc = "act_temp4_p",.type = e_uint16,.addr = 0x20EB    },
    { .desc = "alm_temp5_p",.type = e_uint16,.addr = 0x20EC    },
    { .desc = "act_temp5_p",.type = e_uint16,.addr = 0x20ED    },
    { .desc = "alm_temp6_p",.type = e_uint16,.addr = 0x20EE    },
    { .desc = "act_temp6_p",.type = e_uint16,.addr = 0x20EF    },
    { .desc = "alm_temp7_p",.type = e_uint16,.addr = 0x20F0    },
    { .desc = "act_temp7_p",.type = e_uint16,.addr = 0x20F1    },
    { .desc = "alm_temp8_p",.type = e_uint16,.addr = 0x20F2    },
    { .desc = "act_temp8_p",.type = e_uint16,.addr = 0x20F3    },
    { .desc = "alm_ot_d",.type = e_uint16,.addr = 0x20F4    },
    { .desc = "act_ot_d",.type = e_uint16,.addr = 0x20F5    },

    { .desc = "set_t_nonElec_1",.type = e_uint16,.addr = 0x20F6    },
	{ .desc = "set_t_nonElec_2",.type = e_uint16,.addr = 0x20F7    },

	//控制字
	{ .desc = "modOverCur01.setSwit_oc",.type = e_uint16,.addr = 0x20F8    },
	{ .desc = "modOverCur02.setSwit_oc",.type = e_uint16,.addr = 0x20F9    },
	{ .desc = "setSwit_sgl_oc",.type = e_uint16,.addr = 0x20FA    },
	{ .desc = "setSwit_neg_oc",.type = e_uint16,.addr = 0x20FB    },
	{ .desc = "setSwit_overload",.type = e_uint16,.addr = 0x20FC    },
    { .desc = "setSwit_ov_alarm",.type = e_uint16,.addr = 0x20FD    },
    { .desc = "setSwit_ov",.type = e_uint16,.addr = 0x20FE    },
    { .desc = "setSwit_nv",.type = e_uint16,.addr = 0x20FF    },
    { .desc = "setSwit_exc",.type = e_uint16,.addr = 0x2100    },
	{ .desc = "setSwit_uf",.type = e_uint16,.addr = 0x2101    },
	{ .desc = "setSwit_of",.type = e_uint16,.addr = 0x2102    },
	{ .desc = "setSwit_rvspwr",.type = e_uint16,.addr = 0x2103    },
	{ .desc = "setSwit_overspeed_alarm",.type = e_uint16,.addr = 0x2104    },
	{ .desc = "setSwit_overspeed",.type = e_uint16,.addr = 0x2105    },
    { .desc = "setSwit_alm_ot_ec",.type = e_uint16,.addr = 0x2106    },
    { .desc = "setSwit_act_ot_ec",.type = e_uint16,.addr = 0x2107    },
	{ .desc = "setSwit_nonElec1_trip",.type = e_uint16,.addr = 0x2108    },
	{ .desc = "setSwit_nonElec2_trip",.type = e_uint16,.addr = 0x2109    },
	{ .desc = "setSwit_pt_dx",.type = e_uint16,.addr = 0x210A    },
	{ .desc = "setSwit_ct_dx",.type = e_uint16,.addr = 0x210B    },
	{ .desc = "set_thwj",.type = e_uint16,.addr = 0x210C    },

    //同期参数
	{ .desc = "orig_set_mode_syn",.type = e_uint16,.addr = 0x212C },
	{ .desc = "orig_sps_u",.type = e_uint16,.addr = 0x212D },
	{ .desc = "orig_ss_u",.type = e_uint16,.addr = 0x212E },
	{ .desc = "orig_set_v_h",.type = e_uint16,.addr = 0x212F },
	{ .desc = "orig_set_v_l",.type = e_uint16,.addr = 0x2130 },
	{ .desc = "orig_set_dfdt_dif",.type = e_uint16,.addr = 0x2131 },
	{ .desc = "orig_set_f_dif",.type = e_uint16,.addr = 0x2132 },
	{ .desc = "orig_set_v_dif",.type = e_uint16,.addr = 0x2133 },
	{ .desc = "orig_set_angle",.type = e_uint16,.addr = 0x2134 },
	{ .desc = "orig_set_t_close",.type = e_uint16,.addr = 0x2135 },
	{ .desc = "orig_set_offset_angle",.type = e_uint16,.addr = 0x2136 },
	{ .desc = "orig_set_t_reset",.type = e_uint16,.addr = 0x2137 },
	{ .desc = "setswit_pt_blk",.type = e_uint16,.addr = 0x2138 },
	{ .desc = "setswit_check_s_vol",.type = e_uint16,.addr = 0x2139 },

    //PLC定值
    { .desc = "plc_dz_001",   .type = e_uint16, .addr = 0x2258 },
	{ .desc = "plc_dz_002",   .type = e_uint16, .addr = 0x2259 },
	{ .desc = "plc_dz_003",   .type = e_uint16, .addr = 0x225A },
	{ .desc = "plc_dz_004",   .type = e_uint16, .addr = 0x225B },
	{ .desc = "plc_dz_005",   .type = e_uint16, .addr = 0x225C },
	{ .desc = "plc_dz_006",   .type = e_uint16, .addr = 0x225D },
	{ .desc = "plc_dz_007",   .type = e_uint16, .addr = 0x225E },
	{ .desc = "plc_dz_008",   .type = e_uint16, .addr = 0x225F },
	{ .desc = "plc_dz_009",   .type = e_uint16, .addr = 0x2260 },
	{ .desc = "plc_dz_010",   .type = e_uint16, .addr = 0x2261 },
	{ .desc = "plc_dz_011",   .type = e_uint16, .addr = 0x2262 },
	{ .desc = "plc_dz_012",   .type = e_uint16, .addr = 0x2263 },
	{ .desc = "plc_dz_013",   .type = e_uint16, .addr = 0x2264 },
	{ .desc = "plc_dz_014",   .type = e_uint16, .addr = 0x2265 },
	{ .desc = "plc_dz_015",   .type = e_uint16, .addr = 0x2266 },
	{ .desc = "plc_dz_016",   .type = e_uint16, .addr = 0x2267 },
	{ .desc = "plc_dz_017",   .type = e_uint16, .addr = 0x2268 },
	{ .desc = "plc_dz_018",   .type = e_uint16, .addr = 0x2269 },
	{ .desc = "plc_dz_019",   .type = e_uint16, .addr = 0x226A },
	{ .desc = "plc_dz_020",   .type = e_uint16, .addr = 0x226B },
	{ .desc = "plc_dz_021",   .type = e_uint16, .addr = 0x226C },
	{ .desc = "plc_dz_022",   .type = e_uint16, .addr = 0x226D },
	{ .desc = "plc_dz_023",   .type = e_uint16, .addr = 0x226E },
	{ .desc = "plc_dz_024",   .type = e_uint16, .addr = 0x226F },
	{ .desc = "plc_dz_025",   .type = e_uint16, .addr = 0x2270 },
	{ .desc = "plc_dz_026",   .type = e_uint16, .addr = 0x2271 },
	{ .desc = "plc_dz_027",   .type = e_uint16, .addr = 0x2272 },
	{ .desc = "plc_dz_028",   .type = e_uint16, .addr = 0x2273 },
	{ .desc = "plc_dz_029",   .type = e_uint16, .addr = 0x2274 },
	{ .desc = "plc_dz_030",   .type = e_uint16, .addr = 0x2275 },
	{ .desc = "plc_dz_031",   .type = e_uint16, .addr = 0x2276 },
	{ .desc = "plc_dz_032",   .type = e_uint16, .addr = 0x2277 },
	{ .desc = "plc_dz_033",   .type = e_uint16, .addr = 0x2278 },
	{ .desc = "plc_dz_034",   .type = e_uint16, .addr = 0x2279 },
	{ .desc = "plc_dz_035",   .type = e_uint16, .addr = 0x227A },
	{ .desc = "plc_dz_036",   .type = e_uint16, .addr = 0x227B },
	{ .desc = "plc_dz_037",   .type = e_uint16, .addr = 0x227C },
	{ .desc = "plc_dz_038",   .type = e_uint16, .addr = 0x227D },
	{ .desc = "plc_dz_039",   .type = e_uint16, .addr = 0x227E },
	{ .desc = "plc_dz_040",   .type = e_uint16, .addr = 0x227F },
	{ .desc = "plc_dz_041",   .type = e_uint16, .addr = 0x2280 },
	{ .desc = "plc_dz_042",   .type = e_uint16, .addr = 0x2281 },
	{ .desc = "plc_dz_043",   .type = e_uint16, .addr = 0x2282 },
	{ .desc = "plc_dz_044",   .type = e_uint16, .addr = 0x2283 },
	{ .desc = "plc_dz_045",   .type = e_uint16, .addr = 0x2284 },
	{ .desc = "plc_dz_046",   .type = e_uint16, .addr = 0x2285 },
	{ .desc = "plc_dz_047",   .type = e_uint16, .addr = 0x2286 },
	{ .desc = "plc_dz_048",   .type = e_uint16, .addr = 0x2287 },
	{ .desc = "plc_dz_049",   .type = e_uint16, .addr = 0x2288 },
	{ .desc = "plc_dz_050",   .type = e_uint16, .addr = 0x2289 },
	{ .desc = "plc_dz_051",   .type = e_uint16, .addr = 0x228A },
	{ .desc = "plc_dz_052",   .type = e_uint16, .addr = 0x228B },
	{ .desc = "plc_dz_053",   .type = e_uint16, .addr = 0x228C },
	{ .desc = "plc_dz_054",   .type = e_uint16, .addr = 0x228D },
	{ .desc = "plc_dz_055",   .type = e_uint16, .addr = 0x228E },
	{ .desc = "plc_dz_056",   .type = e_uint16, .addr = 0x228F },
	{ .desc = "plc_dz_057",   .type = e_uint16, .addr = 0x2290 },
	{ .desc = "plc_dz_058",   .type = e_uint16, .addr = 0x2291 },
	{ .desc = "plc_dz_059",   .type = e_uint16, .addr = 0x2292 },
	{ .desc = "plc_dz_060",   .type = e_uint16, .addr = 0x2293 },
	{ .desc = "plc_dz_061",   .type = e_uint16, .addr = 0x2294 },
	{ .desc = "plc_dz_062",   .type = e_uint16, .addr = 0x2295 },
	{ .desc = "plc_dz_063",   .type = e_uint16, .addr = 0x2296 },
	{ .desc = "plc_dz_064",   .type = e_uint16, .addr = 0x2297 },
	{ .desc = "plc_dz_065",   .type = e_uint16, .addr = 0x2298 },
	{ .desc = "plc_dz_066",   .type = e_uint16, .addr = 0x2299 },
	{ .desc = "plc_dz_067",   .type = e_uint16, .addr = 0x229A },
	{ .desc = "plc_dz_068",   .type = e_uint16, .addr = 0x229B },
	{ .desc = "plc_dz_069",   .type = e_uint16, .addr = 0x229C },
	{ .desc = "plc_dz_070",   .type = e_uint16, .addr = 0x229D },
	{ .desc = "plc_dz_071",   .type = e_uint16, .addr = 0x229E },
	{ .desc = "plc_dz_072",   .type = e_uint16, .addr = 0x229F },
	{ .desc = "plc_dz_073",   .type = e_uint16, .addr = 0x22A0 },
	{ .desc = "plc_dz_074",   .type = e_uint16, .addr = 0x22A1 },
	{ .desc = "plc_dz_075",   .type = e_uint16, .addr = 0x22A2 },
	{ .desc = "plc_dz_076",   .type = e_uint16, .addr = 0x22A3 },
	{ .desc = "plc_dz_077",   .type = e_uint16, .addr = 0x22A4 },
	{ .desc = "plc_dz_078",   .type = e_uint16, .addr = 0x22A5 },
	{ .desc = "plc_dz_079",   .type = e_uint16, .addr = 0x22A6 },
	{ .desc = "plc_dz_080",   .type = e_uint16, .addr = 0x22A7 },
	{ .desc = "plc_dz_081",   .type = e_uint16, .addr = 0x22A8 },
	{ .desc = "plc_dz_082",   .type = e_uint16, .addr = 0x22A9 },
	{ .desc = "plc_dz_083",   .type = e_uint16, .addr = 0x22AA },
	{ .desc = "plc_dz_084",   .type = e_uint16, .addr = 0x22AB },
	{ .desc = "plc_dz_085",   .type = e_uint16, .addr = 0x22AC },
	{ .desc = "plc_dz_086",   .type = e_uint16, .addr = 0x22AD },
	{ .desc = "plc_dz_087",   .type = e_uint16, .addr = 0x22AE },
	{ .desc = "plc_dz_088",   .type = e_uint16, .addr = 0x22AF },
	{ .desc = "plc_dz_089",   .type = e_uint16, .addr = 0x22B0 },
	{ .desc = "plc_dz_090",   .type = e_uint16, .addr = 0x22B1 },
	{ .desc = "plc_dz_091",   .type = e_uint16, .addr = 0x22B2 },
	{ .desc = "plc_dz_092",   .type = e_uint16, .addr = 0x22B3 },
	{ .desc = "plc_dz_093",   .type = e_uint16, .addr = 0x22B4 },
	{ .desc = "plc_dz_094",   .type = e_uint16, .addr = 0x22B5 },
	{ .desc = "plc_dz_095",   .type = e_uint16, .addr = 0x22B6 },
	{ .desc = "plc_dz_096",   .type = e_uint16, .addr = 0x22B7 },
	{ .desc = "plc_dz_097",   .type = e_uint16, .addr = 0x22B8 },
	{ .desc = "plc_dz_098",   .type = e_uint16, .addr = 0x22B9 },
	{ .desc = "plc_dz_099",   .type = e_uint16, .addr = 0x22BA },
	{ .desc = "plc_dz_100",   .type = e_uint16, .addr = 0x22BB },
	{ .desc = "plc_dz_101",   .type = e_uint16, .addr = 0x22BC },
	{ .desc = "plc_dz_102",   .type = e_uint16, .addr = 0x22BD },
	{ .desc = "plc_dz_103",   .type = e_uint16, .addr = 0x22BE },
	{ .desc = "plc_dz_104",   .type = e_uint16, .addr = 0x22BF },
	{ .desc = "plc_dz_105",   .type = e_uint16, .addr = 0x22C0 },
	{ .desc = "plc_dz_106",   .type = e_uint16, .addr = 0x22C1 },
	{ .desc = "plc_dz_107",   .type = e_uint16, .addr = 0x22C2 },
	{ .desc = "plc_dz_108",   .type = e_uint16, .addr = 0x22C3 },
	{ .desc = "plc_dz_109",   .type = e_uint16, .addr = 0x22C4 },
	{ .desc = "plc_dz_110",   .type = e_uint16, .addr = 0x22C5 },
	{ .desc = "plc_dz_111",   .type = e_uint16, .addr = 0x22C6 },
	{ .desc = "plc_dz_112",   .type = e_uint16, .addr = 0x22C7 },
	{ .desc = "plc_dz_113",   .type = e_uint16, .addr = 0x22C8 },
	{ .desc = "plc_dz_114",   .type = e_uint16, .addr = 0x22C9 },
	{ .desc = "plc_dz_115",   .type = e_uint16, .addr = 0x22CA },
	{ .desc = "plc_dz_116",   .type = e_uint16, .addr = 0x22CB },
	{ .desc = "plc_dz_117",   .type = e_uint16, .addr = 0x22CC },
	{ .desc = "plc_dz_118",   .type = e_uint16, .addr = 0x22CD },
	{ .desc = "plc_dz_119",   .type = e_uint16, .addr = 0x22CE },
	{ .desc = "plc_dz_120",   .type = e_uint16, .addr = 0x22CF },
	{ .desc = "plc_dz_121",   .type = e_uint16, .addr = 0x22D0 },
	{ .desc = "plc_dz_122",   .type = e_uint16, .addr = 0x22D1 },
	{ .desc = "plc_dz_123",   .type = e_uint16, .addr = 0x22D2 },
	{ .desc = "plc_dz_124",   .type = e_uint16, .addr = 0x22D3 },
	{ .desc = "plc_dz_125",   .type = e_uint16, .addr = 0x22D4 },
	{ .desc = "plc_dz_126",   .type = e_uint16, .addr = 0x22D5 },
	{ .desc = "plc_dz_127",   .type = e_uint16, .addr = 0x22D6 },
	{ .desc = "plc_dz_128",   .type = e_uint16, .addr = 0x22D7 },

    //通信定值 装置参数
	{.desc = "Eth1Ipaddr1",   .type = e_uint16,.addr = 0x2226 	},
	{.desc = "Eth1Ipaddr2",   .type = e_uint16,.addr = 0x2227 	},
	{.desc = "Eth1Ipaddr3",   .type = e_uint16,.addr = 0x2228 	},
	{.desc = "Eth1Ipaddr4",   .type = e_uint16,.addr = 0x2229 	},
	{.desc = "devic_addr",   .type = e_uint16,.addr = 0x222A 	},
	{.desc = "com1_baud",   .type = e_uint16,.addr = 0x222B 	},
	{.desc = "output_Ena",   .type = e_uint16,.addr = 0x222C 	},

    //励磁定值
    //调节定值
    {.desc = "excitation_adjustment_dz_001",     .type = e_uint16,.addr = 0x2190},
    {.desc = "excitation_adjustment_dz_002",     .type = e_uint16,.addr = 0x2191},
    {.desc = "excitation_adjustment_dz_003",     .type = e_uint16,.addr = 0x2192},
    {.desc = "excitation_adjustment_dz_004",     .type = e_uint16,.addr = 0x2193},
    {.desc = "excitation_adjustment_dz_005",     .type = e_uint16,.addr = 0x2194},
    {.desc = "excitation_adjustment_dz_006",     .type = e_uint16,.addr = 0x2195},
    {.desc = "excitation_adjustment_dz_007",     .type = e_uint16,.addr = 0x2196},
    {.desc = "excitation_adjustment_dz_008",     .type = e_uint16,.addr = 0x2197},
    {.desc = "excitation_adjustment_dz_009",     .type = e_uint16,.addr = 0x2198},
    {.desc = "excitation_adjustment_dz_010",     .type = e_uint16,.addr = 0x2199},
    {.desc = "excitation_adjustment_dz_011",     .type = e_uint16,.addr = 0x219A},
    {.desc = "excitation_adjustment_dz_012",     .type = e_uint16,.addr = 0x219B},
    {.desc = "excitation_adjustment_dz_013",     .type = e_uint16,.addr = 0x219C},
    {.desc = "excitation_adjustment_dz_014",     .type = e_uint16,.addr = 0x219D},
    //恒功率定值
    {.desc = "excitation_constant_power_dz_001",     .type = e_uint16,.addr = 0x21C2},
    {.desc = "excitation_constant_power_dz_002",     .type = e_uint16,.addr = 0x21C3},
    //励磁精校系数
    {.desc = "excitation_calib_coe_dz_001",     .type = e_uint16,.addr = 0x21F4},
    {.desc = "excitation_calib_coe_dz_002",     .type = e_uint16,.addr = 0x21F5},
    {.desc = "excitation_calib_coe_dz_003",     .type = e_uint16,.addr = 0x21F6},
    {.desc = "excitation_calib_coe_dz_004",     .type = e_uint16,.addr = 0x21F7},
    {.desc = "excitation_calib_coe_dz_005",     .type = e_uint16,.addr = 0x21F8},

};
#define SYSC_SET_NUMS (sizeof(g_allSetModbusRefTab) / sizeof(s_ModbusRefTab))

ModbusDz_T ModbusDzConfig[] = {
        //具体定值组
        {Mcoeff_setting,                     18,                                  0x2000, 0},//精校定值
        {Pcoeff_setting,                          14,                                  0x2018, 0},//精校定值
        {sys_setting,                         10,                                  0x2032, 0},//系统定值
        {dc_setting,                          19,                                  0x2064, 0},//直流定值
        {protect_setting,                         69,                                  0x20C8, 0},//保护定值
        {TQ_setting,                  14,                                  0x212C, 0},//同期定值
        {PLC_setting,                         128,                                 0x2258, 0},//PLC定值
        {device_setting,                        6,                                   0x2226, 0},//通信定值
        {EXCITATION_ADJUSTMENT_PARAM_NO,     EXCITATION_SET_ADJUSTMENT_PARAM_NUM, 0x2190, 0},//励磁调节参数
        {EXCITATION_CONSTANT_POWER_PARAM_NO, EXCITATION_CONSTANT_POWER_PARAM_NUM, 0x21C2, 0},//励磁恒功率参数
        {EXCITATION_CALIB_COE_NO,            EXCITATION_CALIB_COE_NUM,            0x21F4, 0},//励磁精校系数
};


/*开出传动测试菜单内容表*/
s_ModbusRefTab g_KCTestRefTab[]=
{
    { .desc = "modBo.bi_out01",      .addr = 153  },
    { .desc = "modBo.bi_out02",      .addr = 154  },
    { .desc = "modBo.bi_out03",      .addr = 155  },
    { .desc = "modBo.bi_out04",      .addr = 156  },
    { .desc = "modBo.bi_out05",      .addr = 157  },
    { .desc = "modBo.bi_out06",      .addr = 158  },
    { .desc = "modBo.bi_out07",      .addr = 159  },
    { .desc = "modBo.bi_out08",      .addr = 160  },
    { .desc = "modBo.bi_out09",      .addr = 161  },
    { .desc = "modBo.bi_out10",      .addr = 162  },
    { .desc = "modBo.bi_out11",      .addr = 163  },
    { .desc = "modBo.bi_out12",      .addr = 164  },
    { .desc = "modBo.bi_out13",      .addr = 165  },
    { .desc = "modBo.bi_out14",      .addr = 166  },
    { .desc = "modBo.bi_out15",      .addr = 167  },

    { .desc = "modBo.bi_out17",      .addr = 151  },
    { .desc = "modBo.bi_out18",      .addr = 152  },

    { .desc = "modBo.bi_out19",      .addr = 168  },
    { .desc = "modBo.bi_out20",      .addr = 169  },
    { .desc = "modBo.bi_out21",      .addr = 170  },
    { .desc = "modBo.bi_out22",      .addr = 171  },
    { .desc = "modBo.bi_out23",      .addr = 172  },
    { .desc = "modBo.bi_out24",      .addr = 173  },
    { .desc = "modBo.bi_out25",      .addr = 174  },
    { .desc = "modBo.bi_out26",      .addr = 175  },
    { .desc = "modBo.bi_out27",      .addr = 176  },
    { .desc = "modBo.bi_out28",      .addr = 177  },
    { .desc = "modBo.bi_out29",      .addr = 178  },
    { .desc = "modBo.bi_out30",      .addr = 179  },
    { .desc = "modBo.bi_out31",      .addr = 180  },
    { .desc = "modBo.bi_out32",      .addr = 181  },
    { .desc = "modBo.bi_out33",      .addr = 182  }
};
#define KC_TEST_NUMS (sizeof(g_KCTestRefTab) / sizeof(s_ModbusRefTab))


//DONE
/**********************************************************************************************************
** 函数名称: void	ModbusProtocolRun(void)
** 函数功能: Modbus协议运行函数
** 入口参数: 
** 出口参数: 
**********************************************************************************************************/
void ModbusProtocolRun(void)
{
    if (g_ModbusRunTimer < 50)//50ms运行一次
    {
        return;
    }
    g_ModbusRunTimer = 0;

    ModbusDataAnalysis();
    Modbus_ProLCData();
}


/**********************************************************************************************************
** 函数名称: void	ModbusDataAnalysis(void)
** 函数功能: Modbus协议数据分析函数
** 入口参数: 
** 出口参数: 
**********************************************************************************************************/
void ModbusDataAnalysis(void)
{
    uint32 wRet = 0;
    uint32 ptcnt = 0;
    uint8 *pRecvBuf = NULL;
    uint32 wRecFrameLen = 0;
    uint16 crcCheckNode = 0;
    uint16 calCrcCheckNode = 0;
    uint32 rxrail = 0;
    uint8 funNode = 0;
    uint32 resLen = 0;
    uint8 tempBuff[MODBUSBUFFLEN] = {0};

    wRet = MODBUSBUFFLEN - g_ModbusBuffRcvWr;
    wRet = ModbusGetPortData(g_ModbusRcvBuff + g_ModbusBuffRcvWr, wRet);

    if (wRet == 0)
    {
        return;
    }

    g_ModbusBuffRcvWr += wRet;


    pRecvBuf = g_ModbusRcvBuff;

    while (ptcnt < g_ModbusBuffRcvWr)
    {
        if (pRecvBuf[ptcnt] == g_ModbusAddr)//地址正确
        {
            funNode = pRecvBuf[ptcnt + 1];
            if ((funNode == 0x02) || (funNode == 0x03)
                || (funNode == 0x05) || (funNode == 0x06) || (funNode == 0x10))
            {//功能码正确
                if (((g_ModbusBuffRcvWr - ptcnt) < 8)
                    || ((funNode == 0x10) && ((g_ModbusBuffRcvWr - ptcnt) < (9 + g_ModbusRcvBuff[ptcnt + 6]))))
                {//未接受完全
                    break;
                }

                if (funNode == 0x10)
                {
                    wRecFrameLen = 9 + g_ModbusRcvBuff[ptcnt + 6];
                } else
                {
                    wRecFrameLen = 8;
                }
                crcCheckNode =
                        g_ModbusRcvBuff[ptcnt + wRecFrameLen - 2] + (g_ModbusRcvBuff[ptcnt + wRecFrameLen - 1] << 8);
                //计算校验码
                calCrcCheckNode = CRC16(g_ModbusRcvBuff + ptcnt, wRecFrameLen - 2);
                if (crcCheckNode == calCrcCheckNode)
                {//数据接收完全正确
                    //nPD485Modtimer = 0;
                    switch (funNode)
                    {
                        case 0x02:
                        {
                            memset(&ModbusPro, 0, sizeof(ModbusPro));
                            RTU_CallInputStatusRet(&g_ModbusRcvBuff[ptcnt + 1], 5);
                            break;
                        }
                        case 0x03:
                        {
                            memset(&ModbusPro, 0, sizeof(ModbusPro));
                            RTU_CallHoldingRegRet(&g_ModbusRcvBuff[ptcnt + 1], 5);
                            break;
                        }
                        case 0x05:
                        {
                            memset(&ModbusPro, 0, sizeof(ModbusPro));
                            RTU_WriteSingleCoilRet(&g_ModbusRcvBuff[ptcnt + 1], 5);
                            break;
                        }
                        case 0x06:
                        {
                            memset(&ModbusPro, 0, sizeof(ModbusPro));
                            RTU_WriteSingleRegRet(&g_ModbusRcvBuff[ptcnt + 1], 5);
                            break;
                        }
                        case 0x10:
                        {
                            memset(&ModbusPro, 0, sizeof(ModbusPro));
                            RTU_WriteMulRegRet(&g_ModbusRcvBuff[ptcnt + 1], 6 + g_ModbusRcvBuff[ptcnt + 6]);
                            break;
                        }
                        default:
                        {

                            break;
                        }
                    }
                    ptcnt += wRecFrameLen;
                    rxrail = ptcnt;

                } else
                {//校验码不正确
                    ptcnt++;
                    rxrail = ptcnt;
                }

            } else
            {//功能码不正确
                ptcnt += 1;
                rxrail = ptcnt;
            }
            rxrail = ptcnt;
            continue;
        } else
        {//地址不正确
            ptcnt++;
            rxrail = ptcnt;
        }
    }
    if ((rxrail > 0) && (rxrail < g_ModbusBuffRcvWr))
    {
        resLen = g_ModbusBuffRcvWr - rxrail;
        memcpy(tempBuff, g_ModbusRcvBuff + rxrail, resLen);
        memcpy(g_ModbusRcvBuff, tempBuff, resLen);
        memset(g_ModbusRcvBuff + resLen, 0, rxrail);
        g_ModbusBuffRcvWr = resLen;
    } else if ((rxrail == g_ModbusBuffRcvWr) || ((0 == rxrail) && (MODBUSBUFFLEN == g_ModbusBuffRcvWr)))
    {
        memset(g_ModbusRcvBuff, 0, g_ModbusBuffRcvWr);
        g_ModbusBuffRcvWr = 0;
    }

}

void ModbusInit(void)
{
    int i;
    
    g_ModbusBuffRcvWr = 0;
    g_ModbusAddr = 1;//NOTEL Modbus地址固定为1，只给屏幕使用
    //nPD485Modtimer = TXZEROCS + 1;

    comm_param_confige(0, 9600, 0, 8 ,1 );
    //comm_param_confige(0, 19200, 0, 8 ,1 );  //com0 ,波特率19200， 无校验， 8个数据位，1个停止位

    // 初始化开关量引用表ID
    for (i = 0; i < MODBUS_SWITCH_NUMS; i++)
    {
        g_BIModbusRefTab[i].id = getBiIdByDesc(g_BIModbusRefTab[i].desc);
    }
    
    // 初始化模拟量引用表ID
    for (i = 0; i < MODBUS_ANALOG_NUMS; i++)
    {
        g_ANAModbusRefTab[i].id = getAnaIdByDesc(g_ANAModbusRefTab[i].desc);
    }
    
    // 初始化定值引用表ID
    for (i = 0; i < SYSC_SET_NUMS; i++)
    {
        g_allSetModbusRefTab[i].id = getSetIdByDesc(g_allSetModbusRefTab[i].desc);
    }

    // 初始化控制引用表ID
    for (i = 0; i < MODBUS_CTL_NUMS; i++)
    {
        g_modbusCtlRefTab[i].id = getCtlIdByDesc(g_modbusCtlRefTab[i].desc);
    }
    for (i = 0; i < MODBUS_CTL2_NUMS; i++)
    {
        g_modbusCtlRefTab2[i].id = getSetIdByDesc(g_modbusCtlRefTab2[i].desc);
    }
    for (i = 0; i < MODBUS_CTL3_NUMS; i++)
    {
        g_modbusCtlRefTab3[i].id = getBiIdByDesc(g_modbusCtlRefTab3[i].desc);
    }


    // 初始化开出传动引用表ID
    for (i = 0; i < KC_TEST_NUMS; i++)
    {
        g_KCTestRefTab[i].id = getBiIdByDesc(g_KCTestRefTab[i].desc);
    }

}

//DONE
/**********************************************************************************************************
** 函数名称: int32	ModbusGetPortData(uint8 *dataBuff,uint32 len)
** 函数功能: Modbus协议接收端口数据
** 入口参数: 
**			 dataBuff：接收数据存储区
**			 len：最大接收数据长度
** 出口参数: 返回数据长度：表示传入的参数有误（接收数据区地址为NULL，接收数据最大长度为0）
**********************************************************************************************************/
uint32 ModbusGetPortData(uint8 *dataBuff, uint32 len)
{
    uint16 recvLen = 0;

    // 使用comm0_recv接口接收数据
    recvLen = comm0_recv(g_rx0_buffer, COM_RX_BUFFER_SIZE);

    // 如果接收到数据，将其复制到协议缓冲区
    if (recvLen > 0)
    {
        memcpy(dataBuff, g_rx0_buffer, recvLen);
    }

    return recvLen;
}

/**********************************************************************************************************
** 函数名称: int32	ModbusGetPortData(uint8 *dataBuff,uint32 len)
** 函数功能: Modbus协议接收端口数据
** 入口参数: 
**			 dataBuff：接收数据存储区
**			 len：最大接收数据长度
** 出口参数: 返回数据长度：表示传入的参数有误（接收数据区地址为NULL，接收数据最大长度为0）
**********************************************************************************************************/
uint32 ModbusSendPortData(const uint8 *dataBuff, uint32 len)
{
    uint16 i = 0;

    if (dataBuff == NULL)
    {
        return 0;
    }

    if (len == 0)
    {
        return 0;
    }

    return comm0_send((uint8_t *)dataBuff, len);
}

//DONE
void Modbus_RetErrorFrame(BYTE cFun, BYTE cError)
{
    WORD wCRC = 0;
    uint8 iData[20] = {0};
    iData[0] = g_ModbusAddr;
    iData[1] = 0x80 | cFun;
    iData[2] = cError;

    wCRC = CRC16(iData, 3);
    iData[3] = wCRC & 0xFF;
    iData[4] = wCRC >> 8;
    ModbusSendPortData(iData, 5);
}

//DONE
/**********************************************************************************************************
** 函数名称: void	RTU_CallInputStatusRet(uint8 *iBuf,uint8 iBufLen)
** 函数功能: 处理功能码为0x02的函数
** 入口参数: 
**			 iBuf：接受到的数据指针
**			 iBufLen：传输的数据的长度
** 出口参数: 无
**********************************************************************************************************/
void RTU_CallInputStatusRet(uint8 *iBuf, uint8 iBufLen)
{//目前用于召唤开关量数据
    uint16 wStartAddr = 0;
    uint16 wNeedNum = 0;
    uint8 cError = 0;

    wStartAddr = (iBuf[1] << 8) + iBuf[2];
    wNeedNum = (iBuf[3] << 8) + iBuf[4];


    if (wNeedNum > STATUS_NUM)
    {
        cError = 0x03;
    } else if ((wStartAddr + wNeedNum) > STATUS_NUM)
    {
        cError = 0x02;
    }

    if ((wStartAddr % 8) != 0)
    {
        cError = 0x04;
    }

    if (cError > 0)
    {
        Modbus_RetErrorFrame(2, cError);
        return;
    }
    Modbus_CallSwitchProc(wStartAddr, wNeedNum, 0x02);

}

/**********************************************************************************************************
** 函数名称: void	RTU_CallHoldingRegRet(uint8 *iBuf,uint8 iBufLen)
** 函数功能: 处理功能码为0x03的函数
** 入口参数: 
**			 iBuf：接受到的数据指针
**			 iBufLen：传输的数据的长度
** 出口参数: 无
**********************************************************************************************************/
void RTU_CallHoldingRegRet(uint8 *iBuf, uint8 iBufLen)
{
    uint16 wStartAddr = 0;
    uint16 wNeedNum = 0;
    uint8 cError = 0;
    //ModbusSendPortData((const uint8*)("abcdef"),6);
    wStartAddr = (iBuf[1] << 8) + iBuf[2];
    wNeedNum = (iBuf[3] << 8) + iBuf[4];

    if (wNeedNum > 125)
    {
        cError = 0x03;
    }

    if (wStartAddr < MB_ANA_STARTADDR || wStartAddr > MB_END_ADDR)
    {
        cError = 0x02;
    }

    if (cError > 0)
    {
        Modbus_RetErrorFrame(3, cError);
        return;
    }

    if ((wStartAddr >= MB_ANA_STARTADDR) && (wStartAddr < MB_DZ_STARTADDR))
    {//召唤模拟量
        cError = Modbus_CallAnalogProc(wStartAddr, wNeedNum, 0x03);
    } else if ((wStartAddr >= MB_DZ_STARTADDR) && (wStartAddr < MB_SETTM_STARTADDR))
    {//召唤定值
        cError = Modbus_CallSettingProc(wStartAddr, wNeedNum, 0x03);
    } else if ((wStartAddr >= MB_PARACTL_STARTADDR) && (wStartAddr < MB_SOEINFO_STARTADDR))
    {

    } else if ((wStartAddr >= MB_SOEINFO_STARTADDR) && (wNeedNum <= 2)) //SOE信息
    {

    } else if ((wStartAddr >= MB_SOEDATA_STARTADDR) && (wStartAddr < MB_SOEDATA_ENDADDR))  //SOE数据
    {

    } else if (wStartAddr >= MB_WAVE_STARTADDR) //录波
    {

    }

    if (cError > 0)
    {
        Modbus_RetErrorFrame(3, cError);
        return;
    }

}

//DONE
/**********************************************************************************************************
** 函数名称: void	RTU_WriteSingleCoilRet(uint8 *iBuf,uint8 iBufLen)
** 函数功能: 处理功能码为0x05的函数
** 入口参数: 
**			 iBuf：接受到的数据指针
**			 iBufLen：传输的数据的长度
** 出口参数: 无
**********************************************************************************************************/
void RTU_WriteSingleCoilRet(uint8 *iBuf, uint8 iBufLen)
{
    uint16 wStartAddr = 0;
    uint16 wWriteData = 0;

    wStartAddr = (iBuf[1] << 8) + iBuf[2];
    wWriteData = (iBuf[3] << 8) + iBuf[4];

    if ((wWriteData != 0xff00) && (wWriteData != 0x0000))
    {
        Modbus_RetErrorFrame(5, MB_ILLIGAL_VALUE);
        return;
    }
    Modbus_ProControl(iBuf, iBufLen);

}

//DONE
/**********************************************************************************************************
** 函数名称: void	RTU_WriteSingleRegRet(uint8 *iBuf,uint8 iBufLen)
** 函数功能: 处理功能码为0x06的函数
** 入口参数: 
**			 iBuf：接受到的数据指针
**			 iBufLen：传输的数据的长度
** 出口参数: 无
**********************************************************************************************************/
void RTU_WriteSingleRegRet(uint8 *iBuf, uint8 iBufLen)
{


}

//DONE
/**********************************************************************************************************
** 函数名称: void	RTU_WriteMulRegRet(uint8 *iBuf,uint8 iBufLen)
** 函数功能: 处理功能码为0x10的函数
** 入口参数: 
**			 iBuf：接受到的数据指针
**			 iBufLen：传输的数据的长度
** 出口参数: 无
**********************************************************************************************************/
void RTU_WriteMulRegRet(uint8 *iBuf, uint8 iBufLen)
{
    uint16 wStartAddr = 0;
    uint16 wWriteNum = 0;
    uint8 cError = 0;
    uint16 cBytesCount = 0;

    wStartAddr = (iBuf[1] << 8) + iBuf[2];
    wWriteNum = (iBuf[3] << 8) + iBuf[4];
    cBytesCount = iBuf[5];

    if (cBytesCount != (2 * wWriteNum))
    {
        Modbus_RetErrorFrame(0x10, MB_ILLIGAL_VALUE);
        return;
    }

    if (wStartAddr < MB_ANA_STARTADDR || wStartAddr > MB_END_ADDR)
    {
        Modbus_RetErrorFrame(0x10, MB_ILLIGAL_ADDRESS);
        return;
    }

    if (wStartAddr == MB_SETTM_STARTADDR)
    {//校时命令
        Modbus_CheckTime(iBuf, iBufLen);
    }

    if ((wStartAddr >= MB_PARACTL_STARTADDR) && (wStartAddr < MB_SOEINFO_STARTADDR))
    {//带参数的控制数据

    }

    if (wStartAddr == MB_SOEINFO_STARTADDR || (wStartAddr == (1 + MB_SOEINFO_STARTADDR)))
    {

    }

    if ((wStartAddr >= MB_DZ_STARTADDR) && (wStartAddr < MB_SETTM_STARTADDR))
    {//修改定值
        Modbus_ModifySettingProc(iBuf, iBufLen);
    }


}

//DONE
/**********************************************************************************************************
** 函数名称: uint8	Modbus_CallAnalogProc(uint16	wStartAddr,uint16 wNeedNum,uint8 funNode)
** 函数功能: Modbus协议处理模拟量数据
** 入口参数: 
**			 wStartAddr：	传入的起始地址
**			 wNeedNum：		需要上传的模拟量个数
**			 funNode：		功能码
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
uint8 Modbus_CallAnalogProc(uint16 wStartAddr, uint16 wNeedNum, uint8 funNode)
{
    uint16 i = 0;
    uint16 j = 0;
    uint8 iData[MODBUSBUFFLEN] = {0};
    uint16 fillIndex = 0;
    uint16 iCrcNode = 0;
    uint16 currentAddr = 0;
    int anaValue = 0;
    char point = 0;
    float scaledValue = 0.0f;
    uint32 unsignedValue = 0;
    int32 signedValue = 0;

    // 构建响应帧头
    iData[fillIndex++] = g_ModbusAddr;
    iData[fillIndex++] = funNode;
    iData[fillIndex++] = wNeedNum * 2;  // 每个寄存器占用2个字节

    // 遍历需要的寄存器数量，填充数据
    for (i = 0; i < wNeedNum; i++)
    {
        currentAddr = wStartAddr + i;
        
        // 在引用表中查找对应地址的描述符
        anaValue = 0;
        
        for (j = 0; j < MODBUS_ANALOG_NUMS; j++)
        {
            if (g_ANAModbusRefTab[j].addr == currentAddr)
            {
                // 通过描述符获取模拟量值
                anaValue = getAnaById(g_ANAModbusRefTab[j].id);
                // 像get103AnaVal_F函数一样获取小数位数进行缩放
                point = getAnaPointById(g_ANAModbusRefTab[j].id);

                scaledValue = (float)anaValue;
                while (point > 0)
                {
                    scaledValue /= 10.0f;
                    point--;
                }
                point = g_ANAModbusRefTab[j].point;
                while (point > 0)
                {
                    scaledValue *= 10.0f;
                    point--;
                }
                // 对于Modbus传输，我们传输缩放后的值
                // 为了保持精度，将浮点值转换为合适的整数表示
                anaValue = (int)scaledValue;
                
                // 根据数据类型判断是16位还是32位，以及有符号还是无符号
                if (g_ANAModbusRefTab[j].type == e_int32 || g_ANAModbusRefTab[j].type == e_uint32)
                {
                    // 32位数据，需要占用2个寄存器（4个字节）
                    if (i + 1 >= wNeedNum)
                    {
                        // 剩余空间不足，只填充16位低位数据
                        if (g_ANAModbusRefTab[j].type == e_uint32)
                        {
                            // 无符号32位，取低16位作为无符号数
                            unsignedValue = (uint32)anaValue;
#if STANDARD_MODBUS
                            iData[fillIndex++] = (unsignedValue & 0xFFFF) & 0xff;         // 低字节
                            iData[fillIndex++] = ((unsignedValue & 0xFFFF) >> 8) & 0xff;  // 高字节
#else
                            iData[fillIndex++] = ((unsignedValue & 0xFFFF) >> 8) & 0xff;  // 高字节
                            iData[fillIndex++] = (unsignedValue & 0xFFFF) & 0xff;         // 低字节
#endif
                        }
                        else
                        {
                            // 有符号32位，取低16位但保持符号信息
                            signedValue = (int32)anaValue;
#if STANDARD_MODBUS
                            iData[fillIndex++] = (signedValue & 0xFFFF) & 0xff;         // 低字节
                            iData[fillIndex++] = ((signedValue & 0xFFFF) >> 8) & 0xff;  // 高字节
#else
                            iData[fillIndex++] = ((signedValue & 0xFFFF) >> 8) & 0xff;  // 高字节
                            iData[fillIndex++] = (signedValue & 0xFFFF) & 0xff;         // 低字节
#endif
                        }
                        break;
                    }
                    
                    // 填充32位数据的4个字节，分为两个16位寄存器
                    if (g_ANAModbusRefTab[j].type == e_uint32)
                    {
                        // 无符号32位数据处理
                        unsignedValue = (uint32)anaValue;
                        
                        // 第一个寄存器：低16位
#if STANDARD_MODBUS
                        iData[fillIndex++] = unsignedValue & 0xff;              // 低字节
                        iData[fillIndex++] = (unsignedValue >> 8) & 0xff;       // 次低字节
#else
                        iData[fillIndex++] = (unsignedValue >> 8) & 0xff;       // 次低字节
                        iData[fillIndex++] = unsignedValue & 0xff;              // 低字节
#endif
                        
                        // 第二个寄存器：高16位
#if STANDARD_MODBUS
                        iData[fillIndex++] = (unsignedValue >> 16) & 0xff;      // 次高字节
                        iData[fillIndex++] = (unsignedValue >> 24) & 0xff;      // 高字节
#else
                        iData[fillIndex++] = (unsignedValue >> 24) & 0xff;      // 高字节
                        iData[fillIndex++] = (unsignedValue >> 16) & 0xff;      // 次高字节
#endif
                    }
                    else
                    {
                        // 有符号32位数据处理
                        signedValue = (int32)anaValue;
                        
                        // 第一个寄存器：低16位
#if STANDARD_MODBUS
                        iData[fillIndex++] = signedValue & 0xff;              // 低字节
                        iData[fillIndex++] = (signedValue >> 8) & 0xff;       // 次低字节
#else
                        iData[fillIndex++] = (signedValue >> 8) & 0xff;       // 次低字节
                        iData[fillIndex++] = signedValue & 0xff;              // 低字节
#endif
                        
                        // 第二个寄存器：高16位
#if STANDARD_MODBUS
                        iData[fillIndex++] = (signedValue >> 16) & 0xff;      // 次高字节
                        iData[fillIndex++] = (signedValue >> 24) & 0xff;      // 高字节
#else
                        iData[fillIndex++] = (signedValue >> 24) & 0xff;      // 高字节
                        iData[fillIndex++] = (signedValue >> 16) & 0xff;      // 次高字节
#endif
                    }
                    
                    i++;  // 跳过下一个地址，因为32位数据占用了两个地址
                }
                else
                {
                    // 16位数据，占用1个寄存器（2个字节）
                    if (g_ANAModbusRefTab[j].type == e_uint16 || g_ANAModbusRefTab[j].type == e_uint8)
                    {
                        // 无符号16位数据处理
                        unsignedValue = (uint32)anaValue;
                        // 确保值在无符号16位范围内
                        if (unsignedValue > 0xFFFF) unsignedValue = 0xFFFF;
                        
#if STANDARD_MODBUS
                        iData[fillIndex++] = unsignedValue & 0xff;         // 低字节
                        iData[fillIndex++] = (unsignedValue >> 8) & 0xff;  // 高字节
#else
                        iData[fillIndex++] = (unsignedValue >> 8) & 0xff;  // 高字节
                        iData[fillIndex++] = unsignedValue & 0xff;         // 低字节
#endif
                    }
                    else
                    {
                        // 有符号16位数据处理
                        signedValue = (int32)anaValue;
                        // 确保值在有符号16位范围内
                        if (signedValue > 32767) signedValue = 32767;
                        if (signedValue < -32768) signedValue = -32768;
                        
#if STANDARD_MODBUS
                        iData[fillIndex++] = signedValue & 0xff;         // 低字节
                        iData[fillIndex++] = (signedValue >> 8) & 0xff;  // 高字节
#else
                        iData[fillIndex++] = (signedValue >> 8) & 0xff;  // 高字节
                        iData[fillIndex++] = signedValue & 0xff;         // 低字节
#endif
                    }
                }
                break;
            }
        }
        
        // 如果没有找到对应的引用表项，填充0值
        if (j >= MODBUS_ANALOG_NUMS)
        {
            // 填充16位的0值
#if STANDARD_MODBUS
            iData[fillIndex++] = 0x00;  // 低字节
            iData[fillIndex++] = 0x00;  // 高字节
#else
            iData[fillIndex++] = 0x00;  // 高字节
            iData[fillIndex++] = 0x00;  // 低字节
#endif
        }
    }

    // 计算并添加CRC校验
    iCrcNode = CRC16(iData, fillIndex);
    iData[fillIndex++] = iCrcNode & 0xff;
    iData[fillIndex++] = iCrcNode >> 8;
    
    // 发送数据
    ModbusSendPortData(iData, fillIndex);

    return 0;
}

/**********************************************************************************************************
** 函数名称: uint8	Modbus_CallSwitchProc(uint16	wStartAddr,uint16 wNeedNum,uint8 funNode)
** 函数功能: Modbus协议处理开关量数据
** 入口参数: 
**			 wStartAddr：	传入的起始地址
**			 wNeedNum：		需要上传的开关量个数
**			 funNode：		功能码
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
uint8 Modbus_CallSwitchProc(uint16 wStartAddr, uint16 wNeedNum, uint8 funNode)
{
    uint16 i = 0;
    uint16 j = 0;
    uint8 iData[MODBUSBUFFLEN] = {0};
    uint16 fillIndex = 0;
    uint16 iCrcNode = 0;
    uint8 cRetBytes = 0;
    uint8 bitIndex = 0;
    uint8 byteValue = 0;
    uint16 currentAddr = 0;
    char biValue = 0;

    // 计算需要返回的字节数
    cRetBytes = wNeedNum / 8;
    if ((wNeedNum % 8) > 0)
    {
        cRetBytes++;
    }

    // 构建响应帧头
    iData[fillIndex++] = g_ModbusAddr;
    iData[fillIndex++] = funNode;
    iData[fillIndex++] = cRetBytes;

    // 遍历需要的开关量数量，按位填充数据
    for (i = 0; i < cRetBytes; i++)
    {
        byteValue = 0;
        
        // 每个字节包含8个开关量位
        for (bitIndex = 0; bitIndex < 8; bitIndex++)
        {
            currentAddr = wStartAddr + i * 8 + bitIndex;
            
            // 如果已经超出需要的数量，则填充0
            if ((i * 8 + bitIndex) >= wNeedNum)
            {
                break;
            }
            
            // 在引用表中查找对应地址的描述符
            biValue = 0;
            for (j = 0; j < MODBUS_SWITCH_NUMS; j++)
            {
                if (g_BIModbusRefTab[j].addr == currentAddr)
                {
                    // 通过描述符获取开关量值
                    biValue = getBiById(g_BIModbusRefTab[j].id);
                    break;
                }
            }
            
            // 将开关量值按位设置到字节中
            if (biValue)
            {
                byteValue |= (1 << bitIndex);
            }
        }
        
        iData[fillIndex++] = byteValue;
    }

    // 计算并添加CRC校验
    iCrcNode = CRC16(iData, fillIndex);
    iData[fillIndex++] = iCrcNode & 0xff;
    iData[fillIndex++] = iCrcNode >> 8;
    
    // 发送数据
    ModbusSendPortData(iData, fillIndex);

    return 0;
}

//DONE
/**********************************************************************************************************
** 函数名称: uint8	Modbus_CallSettingProc(uint16	wStartAddr,uint16 wNeedNum,uint8 funNode)
** 函数功能: Modbus协议处理召唤定值数据
** 入口参数: 
**			 wStartAddr：	传入的起始地址
**			 wNeedNum：		需要上传的模拟量个数
**			 funNode：		功能码
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
uint8 Modbus_CallSettingProc(uint16 wStartAddr, uint16 wNeedNum, uint8 funNode)
{
    uint16 i = 0;
    uint8 index = 0;
    uint8 cError = 0;

    for (i = 0; i < MB_PRO_DZ_GROUP_NUM; i++)
    {
        if (ModbusDzConfig[i].wStartAddr == wStartAddr)
        {
            break;
        }
    }

    if (i >= MB_PRO_DZ_GROUP_NUM)
    {
        return 2;
    }

    index = i;

    if (wNeedNum > ModbusDzConfig[index].cDzNum)
    {
        return 2;
    }


    switch (ModbusDzConfig[index].cGroupNo)
    {
        case Mcoeff_setting:
        {
            cError = Modbus_CallProSettingProc(Mcoeff_setting, wNeedNum, funNode);
            break;
        }
        case Pcoeff_setting:
        {
            cError = Modbus_CallProSettingProc(Pcoeff_setting, wNeedNum, funNode);
            break;
        }
        case sys_setting:
        {
            cError = Modbus_CallProSettingProc(sys_setting, wNeedNum, funNode);
            break;
        }
        case dc_setting:
        {
            cError = Modbus_CallProSettingProc(dc_setting, wNeedNum, funNode);
            break;
        }
        case protect_setting:
        {
            cError = Modbus_CallProSettingProc(protect_setting, wNeedNum, funNode);
            break;
        }
        case TQ_setting:
        {
            cError = Modbus_CallProSettingProc(TQ_setting, wNeedNum, funNode);
            break;
        }
        case PLC_setting:
        {
            cError = Modbus_CallProSettingProc(PLC_setting, wNeedNum, funNode);
            break;
        }
        case device_setting:
        {
            cError = Modbus_CallProSettingProc(device_setting, wNeedNum, funNode);
            break;
        }

        case EXCITATION_ADJUSTMENT_PARAM_NO:
        {
            cError = Modbus_CallLCDZCmd(EXCITATION_ADJUSTMENT_PARAM_NO, wNeedNum, wStartAddr, funNode);
            break;
        }
        case EXCITATION_CONSTANT_POWER_PARAM_NO:
        {
            cError = Modbus_CallLCDZCmd(EXCITATION_CONSTANT_POWER_PARAM_NO, wNeedNum, wStartAddr, funNode);
            break;
        }
        case EXCITATION_CALIB_COE_NO:
        {
            cError = Modbus_CallLCDZCmd(EXCITATION_CALIB_COE_NO, wNeedNum, wStartAddr, funNode);
            break;
        }
        default:
        {
            return 2;
        }
    }


    return cError;

}

//DONE
/**********************************************************************************************************
** 函数名称: uint8	Modbus_ModifySettingProc(uint8* iBuf,uint8 iLen)
** 函数功能: Modbus协议处理召唤定值数据
** 入口参数: 
**			 wStartAddr：	传入的起始地址
**			 wNeedNum：		需要上传的模拟量个数
**			 funNode：		功能码
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
uint8 Modbus_ModifySettingProc(uint8 *iBuf, uint8 iLen)
{
    uint16 wStartAddr = 0;
    uint16 wWriteNum = 0;
    uint16 index = 0;
    uint16 i = 0;
    uint8 cError = 0;

    wStartAddr = (iBuf[1] << 8) + iBuf[2];
    wWriteNum = (iBuf[3] << 8) + iBuf[4];

    for (i = 0; i < MB_PRO_DZ_GROUP_NUM; i++)
    {
        if (ModbusDzConfig[i].wStartAddr == wStartAddr)
        {
            break;
        }
    }

    if (i >= MB_PRO_DZ_GROUP_NUM)
    {
        return 2;
    }

    index = i;

    if ((wWriteNum) > ModbusDzConfig[index].cDzNum)
    {
        return 2;
    }

    switch (ModbusDzConfig[index].cGroupNo)
    {
        case Mcoeff_setting:
        {
            cError = Modbus_ModifyProSettingProc(Mcoeff_setting, iBuf, wWriteNum);
            break;
        }
        case Pcoeff_setting:
        {
            cError = Modbus_ModifyProSettingProc(Pcoeff_setting, iBuf, wWriteNum);
            break;
        }
        case sys_setting:
        {
            cError = Modbus_ModifyProSettingProc(sys_setting, iBuf, wWriteNum);
            break;
        }
        case dc_setting:
        {
            cError = Modbus_ModifyProSettingProc(dc_setting, iBuf, wWriteNum);
            break;
        }
        case protect_setting:
        {
            cError = Modbus_ModifyProSettingProc(protect_setting, iBuf, wWriteNum);
            break;
        }
        case TQ_setting:
        {
            cError = Modbus_ModifyProSettingProc(TQ_setting, iBuf, wWriteNum);
            break;
        }
        case PLC_setting:
        {
            cError = Modbus_ModifyProSettingProc(PLC_setting, iBuf, wWriteNum);
            break;
        }
        case device_setting:
        {
            cError = Modbus_ModifyProSettingProc(device_setting, iBuf, wWriteNum);
            break;
        }


        case EXCITATION_ADJUSTMENT_PARAM_NO:
        case EXCITATION_CONSTANT_POWER_PARAM_NO:
        case EXCITATION_CALIB_COE_NO:
        {
            cError = Modbus_ModifyLCDZCmd(iBuf, iLen);
            break;
        }
        default:
        {
            return 2;
        }

    }
    return cError;

}

//DONE
/**********************************************************************************************************
** 函数名称: uint8	Modbus_CallProSettingProc(uint16	wStartAddr,uint16 wNeedNum,uint8 funNode)
** 函数功能: Modbus协议处理召唤主板中的定值数据
** 入口参数: 
**			 wStartAddr：	传入的起始地址
**			 wNeedNum：		需要上传的模拟量个数
**			 funNode：		功能码
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
uint8 Modbus_CallProSettingProc(uint8 inGroupNo, uint16 wNeedNum, uint8 funNode)
{
    uint16 fillIndex = 0;
    uint8 iData[MODBUSBUFFLEN] = {0}; //TODO 把栈空间改成 800暂时解决 ; 原来是 0x400
    uint16 iCrcNode = 0;
    uint16 i = 0;
    uint16 j = 0;
    uint16 currentAddr = 0;
    int setValue = 0;
    uint16 wStartAddr = 0;

    // 根据定值组号获取对应的起始地址
    for (i = 0; i < MB_PRO_DZ_GROUP_NUM; i++)
    {
        if (ModbusDzConfig[i].cGroupNo == inGroupNo)
        {
            wStartAddr = ModbusDzConfig[i].wStartAddr;
            break;
        }
    }

    if (i >= MB_PRO_DZ_GROUP_NUM)
    {
        return 3;  // 未找到对应的定值组
    }

    // 构建响应帧头
    iData[fillIndex++] = g_ModbusAddr;
    iData[fillIndex++] = funNode;
    iData[fillIndex++] = wNeedNum * 2;  // 每个定值占用2个字节

    // 遍历需要的定值数量，填充数据
    for (i = 0; i < wNeedNum; i++)
    {
        currentAddr = wStartAddr + i;

        // 在引用表中查找对应地址的描述符
        setValue = 0;

        for (j = 0; j < SYSC_SET_NUMS; j++)
        {
            if (g_allSetModbusRefTab[j].addr == currentAddr)
            {
                // 通过描述符获取定值
                setValue = getSetByIdModbus(g_allSetModbusRefTab[j].id);//TODO
                break;
            }
        }

        // 将定值按字节序填充到响应数据中
#if STANDARD_MODBUS
        iData[fillIndex++] = setValue & 0xff;         // 低字节
        iData[fillIndex++] = (setValue >> 8) & 0xff;  // 高字节
#else
        iData[fillIndex++] = (setValue >> 8) & 0xff;  // 高字节
        iData[fillIndex++] = setValue & 0xff;         // 低字节
#endif
    }

    // 计算并添加CRC校验
    iCrcNode = CRC16(iData, fillIndex);
    iData[fillIndex++] = iCrcNode & 0xff;
    iData[fillIndex++] = iCrcNode >> 8;
    
    // 发送数据
    ModbusSendPortData(iData, fillIndex);

    return 0;
}

/**********************************************************************************************************
** 函数名称: uint8	Modbus_ModifyProSettingProc(uint8 inGroupNo,uint8* iBuf,uint8 wWriteNum)
** 函数功能: Modbus协议处理召唤主板定值数据
** 入口参数: 
**			 inGroupNo：	修改定值组号
**			 iBuf：			传入的数据地址
**			 wWriteNum：	写入寄存器个数（即定值个数）
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
uint8 Modbus_ModifyProSettingProc(uint8 inGroupNo, uint8 *iBuf, uint8 wWriteNum)
{
    uint16 fillIndex = 0;
    uint8 iData[10] = {0};
    uint16 iCrcNode = 0;
    uint16 i = 0;
    uint16 j = 0;
    uint16 currentAddr = 0;
    int setValue = 0;
    uint16 wStartAddr = 0;

    uint16 buff[20] = {0};

    // 根据定值组号获取对应的起始地址
    for (i = 0; i < MB_PRO_DZ_GROUP_NUM; i++)
    {
        if (ModbusDzConfig[i].cGroupNo == inGroupNo)
        {
            wStartAddr = ModbusDzConfig[i].wStartAddr;
            break;
        }
    }

    if (i >= MB_PRO_DZ_GROUP_NUM)
    {
        return 3;  // 未找到对应的定值组
    }

    // 遍历需要修改的定值数量，设置定值
    for (i = 0; i < wWriteNum; i++)
    {
        currentAddr = wStartAddr + i;
        
        // 从Modbus数据中提取定值
#if STANDARD_MODBUS
        setValue = iBuf[6 + 2 * i] | (iBuf[7 + 2 * i] << 8);  // 低字节在前，高字节在后
#else
        setValue = iBuf[7 + 2 * i] | (iBuf[6 + 2 * i] << 8);  // 高字节在前，低字节在后
#endif

        // 第一次查找，找到起始地址的位置
        for (j = 0; j < SYSC_SET_NUMS; j++)
        {
            if (g_allSetModbusRefTab[j].addr == currentAddr)
            {
                if (g_allSetModbusRefTab[j].id > 0)
                {
                    writeSetToBuf(inGroupNo, 1, g_allSetModbusRefTab[j].id, setValue);
                }

                break;
            }
        }
    }

    // 将缓冲区的定值同步到定值数据集
    assignSetDataSheet(inGroupNo, 1);
    // 将缓冲区的定值写入铁电存储
    writeSetGroup(inGroupNo, 1);

    if (inGroupNo == sys_setting)
    {
        buff[0] = (uint16)getSetByDescModbus("Rated_Power");//300
        buff[1] = (uint16)getSetByDescModbus("Rated_Power_Factor");//850
        buff[2] = (uint16)getSetByDescModbus("sys_un_prim");//38
        buff[3] = (uint16)getSetByDescModbus("sys_un_snd");//380
        buff[3] = buff[3] * 10;
        buff[4] = (uint16)getSetByDescModbus("sys_ux_snd");//380
        buff[4] = buff[4] * 10;

        buff[5] = (uint16)getSetByDescModbus("sys_in_prim");//600
        buff[6] = 0;//beiyong
        buff[7] = (uint16)getSetByDescModbus("Rated_Excitation_Current");//500
        buff[7] = buff[7] / 100;
        buff[8] = (uint16)getSetByDescModbus("shunt_max_current");//5

        Modbus_ModifyLCSeting(EXCITATION_UNIT_PARAM_NO, (uint8 *) (buff),
                              EXCITATION_UNIT_PARAM_NUM);  //g_ModbusDzData+4的意思是去掉前面的两个配置字
    }


    // 构建响应数据
    iData[fillIndex++] = g_ModbusAddr;
    iData[fillIndex++] = 0x10;
    for (i = 0; i < 4; i++)
    {
        iData[fillIndex++] = iBuf[1 + i];
    }

    iCrcNode = CRC16(iData, fillIndex);
    iData[fillIndex++] = iCrcNode & 0xff;
    iData[fillIndex++] = iCrcNode >> 8;
    ModbusSendPortData(iData, fillIndex);
    
    return 0;
}

//DONE
/**********************************************************************************************************
** 函数名称: uint8	Modbus_CheckTime(uint8 *iBuf,uint8 Len)
** 函数功能: Modbus协议处理校时数据
** 入口参数: 
**			 
**			 iBuf：		数据指针
**			 Len：		数据长度
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
uint8 Modbus_CheckTime(uint8 *iBuf, uint8 Len)
{
    uint16 wStartAddr = 0;
    uint16 wWriteNum = 0;
    uint16 cBytesCount = 0;
    uint16 iCrcNode = 0;

}
/**********************************************************************************************************
** 函数名称: uint8	Modbus_ProControl(uint8 *iBuf,uint8 Len)
** 函数功能: Modbus协议处理不带参数的控制数据
** 入口参数: 
**			 
**			 iBuf：		数据指针
**			 Len：		数据长度
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
uint8 Modbus_ProControl(uint8 *iBuf, uint8 Len)
{
    uint16 cEntry = 0;
    uint8 handleFlag = 0;
    uint16 fillIndex = 0;
    uint8 iData[10] = {0};
    uint16 i = 0;
    uint16 iCrcNode = 0;
    int setId;
    uint8 value;

    cEntry = (iBuf[1] << 8) + iBuf[2];
    value = (iBuf[3] == 0xFF)?1:0;

    if((CTLCMD_APP_START <= cEntry) && (cEntry <= CTLCMD_APP_END))
    {
        // 在g_modbusCtlRefTab表中查找对应地址的描述符
        for (i = 0; i < MODBUS_CTL_NUMS; i++)
        {
            if (g_modbusCtlRefTab[i].addr == cEntry)
            {
                //if (g_modbusCtlRefTab[i].id > 0)
                //{
                    // 执行控制操作，参数值为1（执行操作）
                    handleFlag = 1;
                    handleCtlById(g_modbusCtlRefTab[i].id, 1);
                //}
                break;
            }
        }
    }
    else if((CTLCMD_VBI1_START <= cEntry) && (cEntry <= CTLCMD_VBI1_END))
    {
        for (i = 0; i < MODBUS_CTL2_NUMS; i++)
        {
            if (g_modbusCtlRefTab2[i].addr == cEntry)
            {
                handleFlag = 1;
                setId = getSetIdByDesc(g_modbusCtlRefTab2[i].desc);
                writeSetToBuf(soft_setting, 1, setId, (int)value);
                // 将缓冲区的定值同步到定值数据集
                assignSetDataSheet(soft_setting, 1);
                break;
            }
        }
    }
    else if((CTLCMD_VBI2_START <= cEntry) && (cEntry <= CTLCMD_VBI2_END))
    {
        if((CTLCMD_VBI2_LC_START <= cEntry) && (cEntry <= CTLCMD_VBI2_LC_END))
        {
        //TODO励磁
        }
        if((CTLCMD_VBI2_DC_START <= cEntry) && (cEntry <= CTLCMD_VBI2_DC_END))
        {
        //TODO直流
        }
        if((CTLCMD_VBI2_TEMP_START <= cEntry) && (cEntry <= CTLCMD_VBI2_TEMP_END))
        {
            g_channalN = (cEntry - CTLCMD_VBI2_TEMP_START) / 2 + 1;
            g_Crt_Com = (cEntry - CTLCMD_VBI2_TEMP_START) % 2;
        }
		handleFlag = 1;
    }
    else if((CTLCMD_PLC_START <= cEntry) && (cEntry <= CTLCMD_PLC_END))
    {
        for (i = 0; i < MODBUS_CTL3_NUMS; i++)
        {
            if (g_modbusCtlRefTab3[i].addr == cEntry)
            {
                handleFlag = 1;
                setBiById(g_modbusCtlRefTab3[i].id,1);
                break;
            }
        }
    }
    else if((EXCITATION_START_CMD <= cEntry) && (cEntry <= EXCITATION_END_CMD))
    {//励磁命令
        handleFlag = 1;
        Modbus_ProLCControl(cEntry);
    }
    else if((CTLCMD_OUTPUT_START <= cEntry) && (cEntry <= CTLCMD_OUTPUT_END))
    {
		// 出口传动测试 - 命令号与开出位号映射关系：
		// 151->开出17(索引16), 152->开出18(索引17)
		// 153->开出1(索引0), 154->开出2(索引1), ..., 167->开出15(索引14)
		// 168->开出19(索引18), 169->开出20(索引19), ..., 182->开出33(索引32)
		handleFlag = 1;
		uint8_t output_index = 0;
		
		if (cEntry == 151) {
			output_index = 16; // 开出17对应数组索引16
		} else if (cEntry == 152) {
			output_index = 17; // 开出18对应数组索引17
		} else if (cEntry >= 153 && cEntry <= 167) {
			output_index = cEntry - 153; // 开出1-15对应数组索引0-14
		} else if (cEntry >= 168 && cEntry <= 182) {
			output_index = cEntry - 168 + 18; // 开出19-33对应数组索引18-32
		}
		
		if (output_index < 64) // 确保在uint64_t范围内
		{
			g_do_state_test |= (1ULL << output_index); // 设置对应位
		}
    }
    else if((CTLCMD_LC_OUTPUT_START <= cEntry) && (cEntry <= CTLCMD_LC_OUTPUT_END))
	{
		handleFlag = 1;
		s7kmAddOutportCmd((cEntry-CTLCMD_LC_OUTPUT_START+1));
	}


    if (handleFlag == 0)
    {
        Modbus_RetErrorFrame(5, MB_ILLIGAL_ADDRESS);
        return 2;
    }

    // 构建响应数据
    iData[fillIndex++] = g_ModbusAddr;

    for (i = 0; i < Len; i++)
    {
        iData[fillIndex++] = iBuf[i];
    }

    iCrcNode = CRC16(iData, fillIndex);
    iData[fillIndex++] = iCrcNode & 0xff;
    iData[fillIndex++] = iCrcNode >> 8;
    ModbusSendPortData(iData, fillIndex);
    
    return 0;
}

//DONE
/**********************************************************************************************************
** 函数名称: uint8	Modbus_ProLCControl(uint8	cmdNo)
** 函数功能: Modbus协议处理不带参数的励磁控制数据
** 入口参数: 
**			 
**			 cmdNo：		命令号
**			 
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
uint8 Modbus_ProLCControl(uint8 cmdNo)
{
    DEF_CONTROL control;

    if ((cmdNo < EXCITATION_START_CMD) || (cmdNo > EXCITATION_END_CMD))
    {
        return 2;
    }

    control.cmd = cmdNo - EXCITATION_START_CMD;
    control.ctl_type = 0xf2;
    control.operatesrc = OPSRC_HOST;
    control.execobj = COMMUNISOURCE_485;
    control.arglen = 0;
    control.argnum = 0;

    s7kmAddControlCommand(&control, NULL, COMMUNISOURCE_485);

    return 0;
}

//DONE
/**********************************************************************************************************
** 函数名称: uint8	Modbus_CallLCDZCmd(uint8 inGroupNo, uint16 wNeedNum, uint16 wStartAddr, uint8 funNode)
** 函数功能: Modbus协议处理召唤励磁定值
** 入口参数: 
**			 inGroupNo:		定值组组号
**			 wNeedNum：		召唤的字节数
**			 wStartAddr：	召唤的起始地址
**			 funNode:		功能码
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
uint8 Modbus_CallLCDZCmd(uint8 inGroupNo, uint16 wNeedNum, uint16 wStartAddr, uint8 funNode)
{
    CALL_SETTINGS callSetting;
    T_ExcitationDZConfig *p_dzConfig = NULL;
    CHAR returnFlag = 0;

    ModbusPro.MdProNeedNum = wNeedNum;
    ModbusPro.MdProFunNode = funNode;
    ModbusPro.MdProStartAddr = wStartAddr;
    ModbusPro.MdProFlag = MB_PRO_LC_SETTINGRD;
    ModbusPro.MdLCDataProOkFlag = MB_PRO_INI;

    callSetting.groupno = inGroupNo;
    callSetting.execobj = COMMUNISOURCE_485;
    callSetting.operatesrc = OPSRC_HOST;
    callSetting.setno = 0;
    callSetting.psno = 0;
    callSetting.argnum = wNeedNum;
    callSetting.arglen = 0;
    returnFlag = s7kmAddSettingsCommand(&callSetting, NULL, callSetting.execobj);

    if (returnFlag != 1)
    {
        memset(&ModbusPro, 0, sizeof(ModbusPro));
        return 4;
    }

    return 0;
}

//DONE
/**********************************************************************************************************
** 函数名称: uint8 Modbus_ModifyLCDZCmd(uint8* iBuf,uint8 iLen)
** 函数功能: Modbus协议处理更改励磁定值
** 入口参数: 
**			 
**			 iBuf：		召唤的字节数
**			 iLen：	召唤的起始地址
**			 
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
uint8 Modbus_ModifyLCDZCmd(uint8 *iBuf, uint8 iLen)
{
    uint16 wStartAddr = 0;
    uint16 wWriteNum = 0;
    uint16 index = 0;
    uint16 i = 0;
    uint8 cError = 0;

    wStartAddr = (iBuf[1] << 8) + iBuf[2];
    wWriteNum = (iBuf[3] << 8) + iBuf[4];

    for (i = 0; i < MB_PRO_DZ_GROUP_NUM; i++)
    {
        if (ModbusDzConfig[i].wStartAddr == wStartAddr)
        {
            break;
        }
    }

    if (i >= MB_PRO_DZ_GROUP_NUM)
    {
        return 2;
    }

    index = i;

    if ((wWriteNum) > ModbusDzConfig[index].cDzNum)
    {
        return 2;
    }


    for (i = 0; i < wWriteNum; i++)
    {
#if    STANDARD_MODBUS
        g_ModbusDzData[2 * i] = iBuf[6 + 2 * i];
        g_ModbusDzData[2 * i + 1] = iBuf[7 + 2 * i];
#else
        g_ModbusDzData[2*i+1] = iBuf[6+2*i];
        g_ModbusDzData[2*i] = iBuf[7+2*i];
#endif
    }

//	memset(&ModbusPro,0,sizeof(ModbusPro));
    ModbusPro.MdProNeedNum = wWriteNum;
    ModbusPro.MdProFunNode = 0x10;
    ModbusPro.MdProStartAddr = wStartAddr;
    ModbusPro.MdProFlag = MB_PRO_LC_SETTINGWR;
    ModbusPro.MdLCDataProOkFlag = MB_PRO_INI;
    memcpy(ModbusPro.Buff, iBuf, iLen);

    switch (ModbusDzConfig[index].cGroupNo)
    {
        case EXCITATION_ADJUSTMENT_PARAM_NO:
        {
            cError = Modbus_ModifyLCSeting(EXCITATION_ADJUSTMENT_PARAM_NO, g_ModbusDzData, wWriteNum);
            break;
        }
        case EXCITATION_CONSTANT_POWER_PARAM_NO:
        {
            cError = Modbus_ModifyLCSeting(EXCITATION_CONSTANT_POWER_PARAM_NO, g_ModbusDzData, wWriteNum);
            break;
        }
        case EXCITATION_CALIB_COE_NO:
        {
            cError = Modbus_ModifyLCSeting(EXCITATION_CALIB_COE_NO, g_ModbusDzData, wWriteNum);
            break;
        }
        default:
        {
            cError = 3;
            break;
        }
    }

    if (cError > 0)
    {
        memset(&ModbusPro, 0, sizeof(ModbusPro));
        return cError;
    }

    return 0;

}

//DONE
/**********************************************************************************************************
** 函数名称: uint8	Modbus_ModifyLCSeting(uint8 inGroup,uint8* dzData,uint16 writeNum)
** 函数功能: Modbus协议处理将修改励磁定值添加到励磁定值修改处理队列中
** 入口参数: 
**			 inGroup：	定值组号
**			 dzData：	定值数据
**			 writeNum：	修改的个数
**			 
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
uint8 Modbus_ModifyLCSeting(uint8 inGroup, uint8 *dzData, uint16 writeNum)
{
    CALL_SETTINGS callSetting;
    uint16 setDataBuff[50] = {0};
    //return ;
    callSetting.groupno = inGroup;
    callSetting.execobj = COMMUNISOURCE_485;
    callSetting.operatesrc = OPSRC_HOST;
    callSetting.setno = 0;
    callSetting.psno = 0;
    callSetting.argnum = writeNum;

    if (-1 == getLCSettingFromSettingBuff((uint16 *) dzData, (uint16 *) setDataBuff, callSetting.groupno, writeNum))
    {
        return 3;
    }
    callSetting.arglen = writeNum * 2;
    if (-1 == s7kmAddSettingsCommand(&callSetting, (uint8 *) setDataBuff, callSetting.execobj))
    {
        return 3;
    }

    return 0;
}

//DONE
/**********************************************************************************************************
** 函数名称: void	Modbus_SetLCProConfig(uint8 flag)
** 函数功能: 设置modbus中励磁数据被处理成功与否的标志
** 入口参数: 
**			 flag：	数据处理成功与否的标志	1.成功	0.失败
**			 
**			 
**			 
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
void Modbus_SetLCProConfig(uint8 flag)
{
    if ((ModbusPro.MdProFlag != MB_PRO_LC_SETTINGRD)
        && (ModbusPro.MdProFlag != MB_PRO_LC_SETTINGWR)
        && (ModbusPro.MdProFlag != MB_PRO_LC_CONTROL))
    {
        return;
    }

    if (flag)
    {
        ModbusPro.MdLCDataProOkFlag = MB_PRO_LC_DATA_SUC;
    } else
    {
        ModbusPro.MdLCDataProOkFlag = MB_PRO_LC_DATA_DEF;
    }
}

//DONE
/**********************************************************************************************************
** 函数名称: void	Modbus_ProLCData()
** 函数功能: 励磁相关的数据被处理后，向后台返回相应信息
** 入口参数: 
**			 
**			 
**			 
**			 
** 出口参数: 
**********************************************************************************************************/
void Modbus_ProLCData()
{
    uint8 cError = 0;
    uint16 i = 0;
    if ((ModbusPro.MdProFlag != MB_PRO_LC_SETTINGRD)
        && (ModbusPro.MdProFlag != MB_PRO_LC_SETTINGWR)
        && (ModbusPro.MdProFlag != MB_PRO_LC_CONTROL))
    {
        return;
    }
    if (ModbusPro.MdLCDataProOkFlag == MB_PRO_LC_DATA_INI)
    {
        return;
    }
    if (ModbusPro.MdLCDataProOkFlag == MB_PRO_LC_DATA_SUC)
    {
        switch (ModbusPro.MdProFlag)
        {
            case MB_PRO_LC_SETTINGRD:
            {
                cError = Modbus_CallLCSetting(ModbusPro.MdProStartAddr, ModbusPro.MdProNeedNum, ModbusPro.MdProFunNode);
                break;
            }
            case MB_PRO_LC_SETTINGWR:
            {
                cError = Modbus_ModifyLCDZResultPro(ModbusPro.Buff);
                break;
            }
            case MB_PRO_LC_CONTROL:
            {
                break;
            }
            default:
            {
                cError = 4;
                break;
            }
        }
    } else if (ModbusPro.MdLCDataProOkFlag == MB_PRO_LC_DATA_DEF)
    {
        cError = 4;
    }

    memset(&ModbusPro, 0, sizeof(ModbusPro));

    if (cError > 0)
    {
        Modbus_RetErrorFrame(ModbusPro.MdProFunNode, cError);
        return;
    }
}

//DONE
/**********************************************************************************************************
** 函数名称: uint8	Modbus_CallLCSetting(uint16 wStartAddr,uint16 wNeedNum,uint8	funNode)
** 函数功能: 处理Modbus召唤励磁定值
** 入口参数: 
**			 wStartAddr：wStartAddr定值开始地址
**			 wNeedNum：  召唤的定值个数
**			 
**			 
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
uint8 Modbus_CallLCSetting(uint16 wStartAddr, uint16 wNeedNum, uint8 funNode)
{
    uint16 fillIndex = 0;
    uint8 iData[MODBUSBUFFLEN] = {0};
    //P_stcDZ pDzCfgInfo = NULL;
    uint16 iCrcNode = 0;
    uint32 inDzStartAddr = 0;
    uint16 i = 0;
    uint16 j = 0;
    uint16 inGroupNo = 0;

    for (i = 0; i < MB_PRO_DZ_GROUP_NUM; i++)
    {
        if (wStartAddr == ModbusDzConfig[i].wStartAddr)
        {
            inGroupNo = ModbusDzConfig[i].cGroupNo;
            break;
        }
    }
    if (i >= MB_PRO_DZ_GROUP_NUM)
    {
        return 3;
    }

    switch (inGroupNo)
    {
        case EXCITATION_ADJUSTMENT_PARAM_NO:
        {
            inDzStartAddr = EXCITATION_SET_ADJUSTMENT_PARAM_ADDR;
            break;
        }
        case EXCITATION_CONSTANT_POWER_PARAM_NO:
        {
            inDzStartAddr = EXCITATION_CONSTANT_POWER_PARAM_ADDR;
            break;
        }
        case EXCITATION_CALIB_COE_NO:
        {
            inDzStartAddr = EXCITATION_CALIB_COE_ADDR;
            break;
        }
        default:
        {
            return 4;
            break;
        }
    }

    iData[fillIndex++] = g_ModbusAddr;
    iData[fillIndex++] = funNode;
    iData[fillIndex++] = wNeedNum * 2;

    for (i = 0; i < wNeedNum; i++)
    {
#if    STANDARD_MODBUS
        iData[fillIndex++] = g_excitationData.settings[inDzStartAddr + i] & 0xff;
        iData[fillIndex++] = (g_excitationData.settings[inDzStartAddr + i] >> 8) & 0xff;
#else
        iData[fillIndex++] = (g_excitationData.settings[inDzStartAddr+i]>>8)&0xff;
        iData[fillIndex++] = g_excitationData.settings[inDzStartAddr+i]&0xff;
#endif

    }
    iCrcNode = CRC16(iData, fillIndex);
    iData[fillIndex++] = iCrcNode & 0xff;
    iData[fillIndex++] = iCrcNode >> 8;
    ModbusSendPortData(iData, fillIndex);

    return 0;
}

//DONE
/**********************************************************************************************************
** 函数名称: uint8 Modbus_ModifyLCDZResultPro(uint8* iBuf)
** 函数功能: 修改励磁定值后返回结果
** 入口参数: 
**			 iBuf：添加到励磁定值队列时保存下来的从后台下发的数据
**			 
**			 
**			 
** 出口参数: 返回错误码，正确返回0，否者返回大于0的数（表示出错）
**********************************************************************************************************/
uint8 Modbus_ModifyLCDZResultPro(uint8 *iBuf)
{
    uint16 fillIndex = 0;
    uint8 iData[10] = {0};
    uint16 iCrcNode = 0;
    uint16 i = 0;

    iData[fillIndex++] = g_ModbusAddr;
    iData[fillIndex++] = 0x10;
    for (i = 0; i < 4; i++)
    {
        iData[fillIndex++] = iBuf[1 + i];
    }

    iCrcNode = CRC16(iData, fillIndex);
    iData[fillIndex++] = iCrcNode & 0xff;
    iData[fillIndex++] = iCrcNode >> 8;
    ModbusSendPortData(iData, fillIndex);

    return 0;
}

